/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

import (
	"context"
	"testing"
	"time"

	"arien-ai-cli/internal/config"
)

func TestProviderConfig(t *testing.T) {
	config := ProviderConfig{
		APIKey:      "test-key",
		BaseURL:     "https://api.test.com",
		Model:       "test-model",
		Temperature: 0.7,
		MaxTokens:   1000,
		Timeout:     30 * time.Second,
	}

	if config.APIKey != "test-key" {
		t.<PERSON><PERSON><PERSON>("Expected API key 'test-key', got '%s'", config.APIKey)
	}

	if config.BaseURL != "https://api.test.com" {
		t.Errorf("Expected base URL 'https://api.test.com', got '%s'", config.BaseURL)
	}

	if config.Temperature != 0.7 {
		t.Errorf("Expected temperature 0.7, got %f", config.Temperature)
	}
}

func TestChatRequest(t *testing.T) {
	request := ChatRequest{
		Model: "test-model",
		Messages: []Message{
			{
				Role:    "user",
				Content: "Hello",
			},
		},
		Temperature: 0.7,
		MaxTokens:   1000,
	}

	if request.Model != "test-model" {
		t.Errorf("Expected model 'test-model', got '%s'", request.Model)
	}

	if len(request.Messages) != 1 {
		t.Errorf("Expected 1 message, got %d", len(request.Messages))
	}

	if request.Messages[0].Role != "user" {
		t.Errorf("Expected role 'user', got '%s'", request.Messages[0].Role)
	}

	if request.Messages[0].Content != "Hello" {
		t.Errorf("Expected content 'Hello', got '%s'", request.Messages[0].Content)
	}
}

func TestMessage(t *testing.T) {
	message := Message{
		Role:    "assistant",
		Content: "Hello there!",
		ToolCalls: []ToolCall{
			{
				ID:   "call_1",
				Type: "function",
				Function: FunctionCall{
					Name:      "test_function",
					Arguments: `{"arg1": "value1"}`,
				},
			},
		},
	}

	if message.Role != "assistant" {
		t.Errorf("Expected role 'assistant', got '%s'", message.Role)
	}

	if message.Content != "Hello there!" {
		t.Errorf("Expected content 'Hello there!', got '%s'", message.Content)
	}

	if len(message.ToolCalls) != 1 {
		t.Errorf("Expected 1 tool call, got %d", len(message.ToolCalls))
	}

	toolCall := message.ToolCalls[0]
	if toolCall.ID != "call_1" {
		t.Errorf("Expected tool call ID 'call_1', got '%s'", toolCall.ID)
	}

	if toolCall.Function.Name != "test_function" {
		t.Errorf("Expected function name 'test_function', got '%s'", toolCall.Function.Name)
	}
}

func TestTool(t *testing.T) {
	tool := Tool{
		Type: "function",
		Function: Function{
			Name:        "test_function",
			Description: "A test function",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"arg1": map[string]interface{}{
						"type":        "string",
						"description": "First argument",
					},
				},
				"required": []string{"arg1"},
			},
		},
	}

	if tool.Type != "function" {
		t.Errorf("Expected type 'function', got '%s'", tool.Type)
	}

	if tool.Function.Name != "test_function" {
		t.Errorf("Expected function name 'test_function', got '%s'", tool.Function.Name)
	}

	if tool.Function.Description != "A test function" {
		t.Errorf("Expected description 'A test function', got '%s'", tool.Function.Description)
	}

	if tool.Function.Parameters == nil {
		t.Error("Parameters should not be nil")
	}
}

func TestManager(t *testing.T) {
	manager := NewManager()

	if manager == nil {
		t.Fatal("Manager should not be nil")
	}

	// Test empty manager
	providers := manager.ListProviders()
	if len(providers) != 0 {
		t.Errorf("Expected 0 providers, got %d", len(providers))
	}

	// Test getting non-existent provider
	_, err := manager.GetProvider("non-existent")
	if err == nil {
		t.Error("Should get error for non-existent provider")
	}

	// Test getting default provider with no providers
	// First check if config is available
	if config.AppConfig != nil {
		_, err = manager.GetDefaultProvider()
		if err == nil {
			t.Error("Should get error when no providers available")
		}
	} else {
		t.Skip("Config not initialized, skipping default provider test")
	}
}

func TestError(t *testing.T) {
	err := Error{
		Type:    "test_error",
		Message: "This is a test error",
		Code:    "TEST_001",
	}

	if err.Error() != "This is a test error" {
		t.Errorf("Expected error message 'This is a test error', got '%s'", err.Error())
	}

	if err.Type != "test_error" {
		t.Errorf("Expected error type 'test_error', got '%s'", err.Type)
	}

	if err.Code != "TEST_001" {
		t.Errorf("Expected error code 'TEST_001', got '%s'", err.Code)
	}
}

// Mock provider for testing
type MockProvider struct {
	name    string
	models  []string
	hasTools bool
}

func NewMockProvider(name string, models []string, hasTools bool) *MockProvider {
	return &MockProvider{
		name:     name,
		models:   models,
		hasTools: hasTools,
	}
}

func (p *MockProvider) Chat(ctx context.Context, request ChatRequest) (*ChatResponse, error) {
	return &ChatResponse{
		ID:      "mock-response",
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   request.Model,
		Choices: []Choice{
			{
				Index: 0,
				Message: Message{
					Role:    "assistant",
					Content: "Mock response",
				},
				FinishReason: "stop",
			},
		},
		Usage: Usage{
			PromptTokens:     10,
			CompletionTokens: 5,
			TotalTokens:      15,
		},
	}, nil
}

func (p *MockProvider) Stream(ctx context.Context, request ChatRequest) (<-chan StreamResponse, error) {
	responseChan := make(chan StreamResponse, 1)
	go func() {
		defer close(responseChan)
		responseChan <- StreamResponse{
			ID:      "mock-stream",
			Object:  "chat.completion.chunk",
			Created: time.Now().Unix(),
			Model:   request.Model,
			Choices: []StreamChoice{
				{
					Index: 0,
					Delta: MessageDelta{
						Role:    "assistant",
						Content: "Mock stream response",
					},
				},
			},
		}
		responseChan <- StreamResponse{Done: true}
	}()
	return responseChan, nil
}

func (p *MockProvider) GetModels() []string {
	return p.models
}

func (p *MockProvider) GetName() string {
	return p.name
}

func (p *MockProvider) SupportsTools() bool {
	return p.hasTools
}

func TestMockProvider(t *testing.T) {
	provider := NewMockProvider("mock", []string{"mock-model-1", "mock-model-2"}, true)

	if provider.GetName() != "mock" {
		t.Errorf("Expected name 'mock', got '%s'", provider.GetName())
	}

	models := provider.GetModels()
	if len(models) != 2 {
		t.Errorf("Expected 2 models, got %d", len(models))
	}

	if !provider.SupportsTools() {
		t.Error("Expected provider to support tools")
	}

	// Test chat
	ctx := context.Background()
	request := ChatRequest{
		Model: "mock-model-1",
		Messages: []Message{
			{Role: "user", Content: "Hello"},
		},
	}

	response, err := provider.Chat(ctx, request)
	if err != nil {
		t.Fatalf("Chat failed: %v", err)
	}

	if response.Model != "mock-model-1" {
		t.Errorf("Expected model 'mock-model-1', got '%s'", response.Model)
	}

	if len(response.Choices) != 1 {
		t.Errorf("Expected 1 choice, got %d", len(response.Choices))
	}

	// Test stream
	streamChan, err := provider.Stream(ctx, request)
	if err != nil {
		t.Fatalf("Stream failed: %v", err)
	}

	var responses []StreamResponse
	for response := range streamChan {
		responses = append(responses, response)
	}

	if len(responses) != 2 {
		t.Errorf("Expected 2 stream responses, got %d", len(responses))
	}

	if !responses[1].Done {
		t.Error("Last response should be marked as done")
	}
}
