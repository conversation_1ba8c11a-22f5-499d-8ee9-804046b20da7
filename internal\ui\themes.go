/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"github.com/charmbracelet/lipgloss"
)

// Theme represents a UI theme
type Theme struct {
	Name        string
	Description string
	Primary     lipgloss.Color
	Secondary   lipgloss.Color
	Accent      lipgloss.Color
	Background  lipgloss.Color
	Foreground  lipgloss.Color
	Success     lipgloss.Color
	Warning     lipgloss.Color
	Error       lipgloss.Color
	Muted       lipgloss.Color
	Border      lipgloss.Color
}

// Available themes
var themes = map[string]Theme{
	"default": {
		Name:        "Default",
		Description: "Clean and modern default theme",
		Primary:     lipgloss.Color("#007ACC"),
		Secondary:   lipgloss.Color("#6C7B7F"),
		Accent:      lipgloss.Color("#FF6B6B"),
		Background:  lipgloss.Color("#FFFFFF"),
		Foreground:  lipgloss.Color("#2D3748"),
		Success:     lipgloss.Color("#48BB78"),
		Warning:     lipgloss.Color("#ED8936"),
		Error:       lipgloss.Color("#F56565"),
		Muted:       lipgloss.Color("#A0AEC0"),
		Border:      lipgloss.Color("#E2E8F0"),
	},
	"dark": {
		Name:        "Dark",
		Description: "Dark theme for low-light environments",
		Primary:     lipgloss.Color("#61DAFB"),
		Secondary:   lipgloss.Color("#8B949E"),
		Accent:      lipgloss.Color("#F78C6C"),
		Background:  lipgloss.Color("#0D1117"),
		Foreground:  lipgloss.Color("#F0F6FC"),
		Success:     lipgloss.Color("#56D364"),
		Warning:     lipgloss.Color("#E3B341"),
		Error:       lipgloss.Color("#F85149"),
		Muted:       lipgloss.Color("#6E7681"),
		Border:      lipgloss.Color("#30363D"),
	},
	"cyberpunk": {
		Name:        "Cyberpunk",
		Description: "Neon-inspired cyberpunk theme",
		Primary:     lipgloss.Color("#00FFFF"),
		Secondary:   lipgloss.Color("#FF00FF"),
		Accent:      lipgloss.Color("#FFFF00"),
		Background:  lipgloss.Color("#000000"),
		Foreground:  lipgloss.Color("#00FF00"),
		Success:     lipgloss.Color("#00FF00"),
		Warning:     lipgloss.Color("#FFFF00"),
		Error:       lipgloss.Color("#FF0000"),
		Muted:       lipgloss.Color("#808080"),
		Border:      lipgloss.Color("#00FFFF"),
	},
	"ocean": {
		Name:        "Ocean",
		Description: "Calm ocean-inspired blue theme",
		Primary:     lipgloss.Color("#0077BE"),
		Secondary:   lipgloss.Color("#4A90A4"),
		Accent:      lipgloss.Color("#87CEEB"),
		Background:  lipgloss.Color("#F0F8FF"),
		Foreground:  lipgloss.Color("#2F4F4F"),
		Success:     lipgloss.Color("#20B2AA"),
		Warning:     lipgloss.Color("#DAA520"),
		Error:       lipgloss.Color("#DC143C"),
		Muted:       lipgloss.Color("#708090"),
		Border:      lipgloss.Color("#B0C4DE"),
	},
	"forest": {
		Name:        "Forest",
		Description: "Nature-inspired green theme",
		Primary:     lipgloss.Color("#228B22"),
		Secondary:   lipgloss.Color("#556B2F"),
		Accent:      lipgloss.Color("#9ACD32"),
		Background:  lipgloss.Color("#F5FFFA"),
		Foreground:  lipgloss.Color("#2F4F2F"),
		Success:     lipgloss.Color("#32CD32"),
		Warning:     lipgloss.Color("#FFD700"),
		Error:       lipgloss.Color("#DC143C"),
		Muted:       lipgloss.Color("#8FBC8F"),
		Border:      lipgloss.Color("#90EE90"),
	},
	"sunset": {
		Name:        "Sunset",
		Description: "Warm sunset-inspired theme",
		Primary:     lipgloss.Color("#FF6347"),
		Secondary:   lipgloss.Color("#FF7F50"),
		Accent:      lipgloss.Color("#FFD700"),
		Background:  lipgloss.Color("#FFF8DC"),
		Foreground:  lipgloss.Color("#8B4513"),
		Success:     lipgloss.Color("#32CD32"),
		Warning:     lipgloss.Color("#FF8C00"),
		Error:       lipgloss.Color("#DC143C"),
		Muted:       lipgloss.Color("#D2B48C"),
		Border:      lipgloss.Color("#DEB887"),
	},
}

var currentTheme Theme

// GetTheme returns the current theme
func GetTheme() Theme {
	return currentTheme
}

// SetTheme sets the current theme
func SetTheme(name string) bool {
	if theme, exists := themes[name]; exists {
		currentTheme = theme
		return true
	}
	return false
}

// GetAvailableThemes returns all available themes
func GetAvailableThemes() map[string]Theme {
	return themes
}

// GetThemeNames returns all theme names
func GetThemeNames() []string {
	var names []string
	for name := range themes {
		names = append(names, name)
	}
	return names
}

// Initialize sets the default theme
func InitializeThemes() {
	currentTheme = themes["default"]
}

// Style helpers using current theme

func PrimaryStyle() lipgloss.Style {
	return lipgloss.NewStyle().Foreground(currentTheme.Primary)
}

func SecondaryStyle() lipgloss.Style {
	return lipgloss.NewStyle().Foreground(currentTheme.Secondary)
}

func AccentStyle() lipgloss.Style {
	return lipgloss.NewStyle().Foreground(currentTheme.Accent)
}

func SuccessStyle() lipgloss.Style {
	return lipgloss.NewStyle().Foreground(currentTheme.Success)
}

func WarningStyle() lipgloss.Style {
	return lipgloss.NewStyle().Foreground(currentTheme.Warning)
}

func ErrorStyle() lipgloss.Style {
	return lipgloss.NewStyle().Foreground(currentTheme.Error)
}

func MutedStyle() lipgloss.Style {
	return lipgloss.NewStyle().Foreground(currentTheme.Muted)
}

func BorderStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(currentTheme.Border)
}

func HeaderStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Foreground(currentTheme.Primary).
		Bold(true).
		Padding(0, 1)
}

func MessageStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Foreground(currentTheme.Foreground).
		Padding(0, 1)
}

func UserMessageStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Foreground(currentTheme.Primary).
		Bold(true).
		Padding(0, 1)
}

func AssistantMessageStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Foreground(currentTheme.Secondary).
		Padding(0, 1)
}

func InputStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(currentTheme.Border).
		Padding(0, 1)
}

func StatusStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Foreground(currentTheme.Muted).
		Italic(true)
}
