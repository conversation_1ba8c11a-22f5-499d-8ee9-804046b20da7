# Contributing to Arien AI CLI

Thank you for your interest in contributing to Arien AI CLI! This document provides guidelines and information for contributors.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Code Style](#code-style)
- [Testing](#testing)
- [Adding Features](#adding-features)
- [Submitting Changes](#submitting-changes)

## Getting Started

### Prerequisites

- Go 1.24 or later
- Git
- Make (optional, for using Makefile)

### Development Setup

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd arien-ai-cli
   ```

2. **Install dependencies:**
   ```bash
   make deps
   # or
   go mod download && go mod tidy
   ```

3. **Set up development environment:**
   ```bash
   make setup
   ```

4. **Build the project:**
   ```bash
   make build
   # or
   go build -o build/arien-ai .
   ```

5. **Run tests:**
   ```bash
   make test
   # or
   go test ./...
   ```

## Code Style

### Go Code Standards

- Follow standard Go formatting (`go fmt`)
- Use meaningful variable and function names
- Add comments for exported functions and types
- Keep functions focused and small
- Handle errors appropriately

### License Headers

All files must include the MIT license header:

```go
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
```

### Package Organization

- `cmd/`: CLI command definitions
- `internal/auth/`: Authentication management
- `internal/config/`: Configuration system
- `internal/llm/`: LLM provider integrations
- `internal/tools/`: Function calling tools
- `internal/ui/`: Terminal UI components
- `internal/logger/`: Logging system

## Testing

### Writing Tests

- Write tests for all new functionality
- Use table-driven tests where appropriate
- Mock external dependencies
- Test both success and error cases
- Aim for high test coverage

### Test Structure

```go
func TestFunctionName(t *testing.T) {
    // Setup
    
    // Test cases
    tests := []struct {
        name     string
        input    InputType
        expected ExpectedType
        wantErr  bool
    }{
        // Test cases
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

### Running Tests

```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Run specific package tests
go test ./internal/config

# Run with verbose output
go test -v ./...
```

## Adding Features

### Adding New LLM Providers

1. **Create provider implementation:**
   ```go
   // internal/llm/newprovider.go
   type NewProvider struct {
       client *http.Client
       config ProviderConfig
   }
   
   func (p *NewProvider) Chat(ctx context.Context, request ChatRequest) (*ChatResponse, error) {
       // Implementation
   }
   
   // Implement other Provider interface methods
   ```

2. **Add to manager:**
   ```go
   // internal/llm/manager.go
   func (m *Manager) Initialize() error {
       // Add provider initialization
       if cred, ok := auth.GetCredentials("newprovider"); ok {
           provider := NewNewProvider(config)
           m.providers["newprovider"] = provider
       }
   }
   ```

3. **Update authentication:**
   ```go
   // internal/auth/auth.go
   func authenticateProvider(provider string) error {
       switch provider {
       case "newprovider":
           return authenticateAPIKey(provider, "New Provider API Key")
       }
   }
   ```

4. **Add tests:**
   ```go
   // internal/llm/newprovider_test.go
   func TestNewProvider(t *testing.T) {
       // Test implementation
   }
   ```

### Adding New Tools

1. **Create tool implementation:**
   ```go
   // internal/tools/newtool.go
   type NewTool struct {
       *BaseTool
   }
   
   func NewNewTool() *NewTool {
       parameters := map[string]interface{}{
           // JSON schema for parameters
       }
       
       return &NewTool{
           BaseTool: NewBaseTool(
               "new_tool",
               "Description of the new tool",
               parameters,
           ),
       }
   }
   
   func (t *NewTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
       // Implementation
   }
   ```

2. **Register in manager:**
   ```go
   // internal/tools/manager.go
   func (m *Manager) Initialize() error {
       // Add tool registration
       m.registry.Register(NewNewTool())
   }
   ```

3. **Add tests:**
   ```go
   // internal/tools/newtool_test.go
   func TestNewTool(t *testing.T) {
       // Test implementation
   }
   ```

### Adding New UI Themes

1. **Add theme definition:**
   ```go
   // internal/ui/themes.go
   var themes = map[string]Theme{
       "newtheme": {
           Name:        "New Theme",
           Description: "Description of the new theme",
           Primary:     lipgloss.Color("#COLOR1"),
           Secondary:   lipgloss.Color("#COLOR2"),
           // ... other colors
       },
   }
   ```

2. **Test the theme:**
   ```go
   // internal/ui/themes_test.go
   func TestNewTheme(t *testing.T) {
       // Test theme properties
   }
   ```

## Submitting Changes

### Pull Request Process

1. **Fork the repository**

2. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes:**
   - Write code following the style guidelines
   - Add tests for new functionality
   - Update documentation if needed

4. **Test your changes:**
   ```bash
   make test
   make lint
   ```

5. **Commit your changes:**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

6. **Push to your fork:**
   ```bash
   git push origin feature/your-feature-name
   ```

7. **Create a pull request**

### Commit Message Format

Use conventional commit format:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `test:` - Test additions/changes
- `refactor:` - Code refactoring
- `style:` - Code style changes
- `chore:` - Maintenance tasks

Examples:
```
feat: add support for new LLM provider
fix: resolve authentication timeout issue
docs: update API documentation
test: add tests for tool execution
```

### Pull Request Guidelines

- Provide clear description of changes
- Reference any related issues
- Include screenshots for UI changes
- Ensure all tests pass
- Update documentation if needed
- Keep changes focused and atomic

## Code Review

### Review Criteria

- Code follows style guidelines
- Tests are comprehensive
- Documentation is updated
- No breaking changes (unless discussed)
- Performance considerations
- Security implications

### Review Process

1. Automated checks must pass
2. At least one maintainer review required
3. Address review feedback
4. Squash commits if requested
5. Merge after approval

## Getting Help

- Open an issue for bugs or feature requests
- Join discussions in existing issues
- Ask questions in pull request comments
- Contact maintainers for major changes

## License

By contributing, you agree that your contributions will be licensed under the MIT License.

Thank you for contributing to Arien AI CLI!
