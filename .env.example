# Arien AI CLI Environment Variables
# Copyright 2025 Arien LLC
# License: MIT

# Copy this file to .env and fill in your API keys

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# DeepSeek Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Custom base URLs
# OPENAI_BASE_URL=https://api.openai.com/v1
# DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
# ANTHROPIC_BASE_URL=https://api.anthropic.com/v1
# GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1

# Debug mode
# DEBUG=false
