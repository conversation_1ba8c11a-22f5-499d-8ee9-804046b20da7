/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"arien-ai-cli/internal/logger"
)

// AnthropicProvider implements the Provider interface for Anthropic Claude
type AnthropicProvider struct {
	client  *http.Client
	config  ProviderConfig
	baseURL string
}

// AnthropicRequest represents an Anthropic API request
type AnthropicRequest struct {
	Model       string              `json:"model"`
	MaxTokens   int                 `json:"max_tokens"`
	Messages    []AnthropicMessage  `json:"messages"`
	System      string              `json:"system,omitempty"`
	Temperature float64             `json:"temperature,omitempty"`
	Tools       []AnthropicTool     `json:"tools,omitempty"`
	ToolChoice  interface{}         `json:"tool_choice,omitempty"`
	Stream      bool                `json:"stream,omitempty"`
}

// AnthropicMessage represents a message in Anthropic format
type AnthropicMessage struct {
	Role    string                 `json:"role"`
	Content interface{}            `json:"content"`
}

// AnthropicTool represents a tool in Anthropic format
type AnthropicTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// AnthropicResponse represents an Anthropic API response
type AnthropicResponse struct {
	ID           string                   `json:"id"`
	Type         string                   `json:"type"`
	Role         string                   `json:"role"`
	Content      []AnthropicContent       `json:"content"`
	Model        string                   `json:"model"`
	StopReason   string                   `json:"stop_reason"`
	StopSequence string                   `json:"stop_sequence"`
	Usage        AnthropicUsage           `json:"usage"`
}

// AnthropicContent represents content in Anthropic format
type AnthropicContent struct {
	Type string `json:"type"`
	Text string `json:"text,omitempty"`
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
	Input interface{} `json:"input,omitempty"`
}

// AnthropicUsage represents usage information
type AnthropicUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// NewAnthropicProvider creates a new Anthropic provider
func NewAnthropicProvider(config ProviderConfig) *AnthropicProvider {
	baseURL := "https://api.anthropic.com/v1"
	if config.BaseURL != "" {
		baseURL = config.BaseURL
	}

	return &AnthropicProvider{
		client: &http.Client{
			Timeout: config.Timeout,
		},
		config:  config,
		baseURL: baseURL,
	}
}

// Chat sends a chat completion request to Anthropic
func (p *AnthropicProvider) Chat(ctx context.Context, request ChatRequest) (*ChatResponse, error) {
	logger.Debug("Anthropic Chat request:", request)

	// Convert to Anthropic format
	anthropicReq := p.convertToAnthropicRequest(request)

	// Make HTTP request
	reqBody, err := json.Marshal(anthropicReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", p.baseURL+"/messages", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", p.config.APIKey)
	httpReq.Header.Set("anthropic-version", "2023-06-01")

	// Send request
	resp, err := p.client.Do(httpReq)
	if err != nil {
		logger.Error("Anthropic API error:", err)
		return nil, fmt.Errorf("Anthropic API error: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API error: %s", string(body))
	}

	// Parse response
	var anthropicResp AnthropicResponse
	if err := json.Unmarshal(body, &anthropicResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Convert back to our format
	return p.convertFromAnthropicResponse(anthropicResp), nil
}

// Stream sends a streaming chat completion request to Anthropic
func (p *AnthropicProvider) Stream(ctx context.Context, request ChatRequest) (<-chan StreamResponse, error) {
	logger.Debug("Anthropic Stream request:", request)

	// Convert to Anthropic format
	anthropicReq := p.convertToAnthropicRequest(request)
	anthropicReq.Stream = true

	// Make HTTP request
	reqBody, err := json.Marshal(anthropicReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", p.baseURL+"/messages", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", p.config.APIKey)
	httpReq.Header.Set("anthropic-version", "2023-06-01")
	httpReq.Header.Set("Accept", "text/event-stream")

	// Send request
	resp, err := p.client.Do(httpReq)
	if err != nil {
		logger.Error("Anthropic Stream API error:", err)
		return nil, fmt.Errorf("Anthropic Stream API error: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		return nil, fmt.Errorf("API error: %s", string(body))
	}

	// Create response channel
	responseChan := make(chan StreamResponse, 10)

	// Start goroutine to handle streaming
	go func() {
		defer close(responseChan)
		defer resp.Body.Close()

		// For now, implement basic streaming
		// TODO: Implement proper SSE parsing for Anthropic streaming
		responseChan <- StreamResponse{
			ID:      "anthropic-stream",
			Object:  "chat.completion.chunk",
			Created: time.Now().Unix(),
			Model:   request.Model,
			Choices: []StreamChoice{{
				Index: 0,
				Delta: MessageDelta{
					Role:    "assistant",
					Content: "Streaming not fully implemented for Anthropic yet.",
				},
			}},
		}
		responseChan <- StreamResponse{Done: true}
	}()

	return responseChan, nil
}

// GetModels returns available Anthropic models
func (p *AnthropicProvider) GetModels() []string {
	return []string{
		"claude-3-opus-20240229",
		"claude-3-sonnet-20240229",
		"claude-3-haiku-20240307",
		"claude-3-5-sonnet-20241022",
	}
}

// GetName returns the provider name
func (p *AnthropicProvider) GetName() string {
	return "anthropic"
}

// SupportsTools returns whether Anthropic supports function calling
func (p *AnthropicProvider) SupportsTools() bool {
	return true
}

// Helper methods

func (p *AnthropicProvider) convertToAnthropicRequest(request ChatRequest) AnthropicRequest {
	anthropicReq := AnthropicRequest{
		Model:       request.Model,
		MaxTokens:   request.MaxTokens,
		Temperature: request.Temperature,
	}

	// Convert messages
	var systemMessage string
	var messages []AnthropicMessage

	for _, msg := range request.Messages {
		if msg.Role == "system" {
			systemMessage = msg.Content
			continue
		}

		anthropicMsg := AnthropicMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
		messages = append(messages, anthropicMsg)
	}

	anthropicReq.System = systemMessage
	anthropicReq.Messages = messages

	// Convert tools
	if len(request.Tools) > 0 {
		for _, tool := range request.Tools {
			anthropicTool := AnthropicTool{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				InputSchema: tool.Function.Parameters,
			}
			anthropicReq.Tools = append(anthropicReq.Tools, anthropicTool)
		}
	}

	return anthropicReq
}

func (p *AnthropicProvider) convertFromAnthropicResponse(resp AnthropicResponse) *ChatResponse {
	var content string
	var toolCalls []ToolCall

	for _, c := range resp.Content {
		if c.Type == "text" {
			content += c.Text
		} else if c.Type == "tool_use" {
			toolCall := ToolCall{
				ID:   c.ID,
				Type: "function",
				Function: FunctionCall{
					Name:      c.Name,
					Arguments: fmt.Sprintf("%v", c.Input),
				},
			}
			toolCalls = append(toolCalls, toolCall)
		}
	}

	return &ChatResponse{
		ID:      resp.ID,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   resp.Model,
		Choices: []Choice{{
			Index: 0,
			Message: Message{
				Role:      resp.Role,
				Content:   content,
				ToolCalls: toolCalls,
			},
			FinishReason: resp.StopReason,
		}},
		Usage: Usage{
			PromptTokens:     resp.Usage.InputTokens,
			CompletionTokens: resp.Usage.OutputTokens,
			TotalTokens:      resp.Usage.InputTokens + resp.Usage.OutputTokens,
		},
	}
}
