# Arien AI CLI

A comprehensive AI-powered CLI terminal tool with LLM integrations and function calling capabilities.

## Features

- **Multiple LLM Providers**: Support for DeepSeek, OpenAI, Google Gemini, and Anthropic Claude
- **Interactive Chat Interface**: Beautiful terminal UI with themes and live visualization
- **Function Calling**: Built-in tools for system operations, file management, calculations, and more
- **Authentication Management**: Secure API key and OAuth authentication
- **Customizable Themes**: Multiple UI themes with live preview
- **Modular Architecture**: Clean separation of concerns with extensible design

## Installation

### Prerequisites

- Go 1.24 or later
- API keys for your preferred LLM providers

### Build from Source

```bash
git clone <repository-url>
cd arien-ai-cli
go mod tidy
go build -o arien-ai
```

## Quick Start

1. **Authenticate with LLM providers:**
   ```bash
   ./arien-ai auth login
   ```

2. **Start interactive chat:**
   ```bash
   ./arien-ai
   ```

3. **Or start chat with specific provider:**
   ```bash
   ./arien-ai chat --provider openai --model gpt-4
   ```

## Commands

### Authentication
```bash
# Interactive authentication setup
arien-ai auth login

# Check authentication status
arien-ai auth status

# Logout from specific provider
arien-ai auth logout --provider openai

# Logout from all providers
arien-ai auth logout
```

### Chat
```bash
# Start interactive chat (default provider/model)
arien-ai

# Start chat with specific provider and model
arien-ai chat --provider deepseek --model deepseek-chat

# Start chat with specific theme
arien-ai chat --theme cyberpunk
```

### Themes
```bash
# Show theme selector with live preview
arien-ai theme
```

### Configuration
```bash
# Show current configuration
arien-ai config show

# Set configuration values
arien-ai config set default_provider openai
arien-ai config set default_model gpt-4
arien-ai config set default_theme dark

# Reset to defaults
arien-ai config reset
```

### Tools
```bash
# List available tools
arien-ai tools list

# Get tool information
arien-ai tools info get_system_info

# Test a tool
arien-ai tools test calculate '{"operation":"add","operands":[2,3]}'
```

## Supported LLM Providers

### OpenAI
- **Models**: GPT-4, GPT-4 Turbo, GPT-3.5 Turbo
- **Authentication**: API Key
- **Function Calling**: ✅ Supported

### DeepSeek
- **Models**: deepseek-chat, deepseek-reasoner, deepseek-coder
- **Authentication**: API Key
- **Function Calling**: ✅ Supported

### Anthropic Claude
- **Models**: Claude-3 Opus, Claude-3 Sonnet, Claude-3 Haiku, Claude-3.5 Sonnet
- **Authentication**: API Key
- **Function Calling**: ✅ Supported

### Google Gemini
- **Models**: Gemini Pro, Gemini Pro Vision, Gemini 1.5 Pro, Gemini 1.5 Flash
- **Authentication**: API Key / OAuth
- **Function Calling**: ✅ Supported

## Available Tools

The CLI includes built-in tools that LLMs can call to perform various tasks:

### System Tools
- **get_system_info**: Get OS, CPU, memory, disk, and network information
- **execute_command**: Execute system commands with timeout
- **get_current_time**: Get current date/time in various formats

### File Tools
- **read_file**: Read file contents
- **write_file**: Write content to files
- **list_directory**: List directory contents

### Utility Tools
- **calculate**: Perform mathematical calculations
- **text_operation**: Text manipulation (length, case, reverse, split, join, replace)
- **network_operation**: Network operations (ping, HTTP requests)

## Themes

Available themes with live preview:
- **Default**: Clean and modern
- **Dark**: Dark theme for low-light environments
- **Cyberpunk**: Neon-inspired cyberpunk theme
- **Ocean**: Calm ocean-inspired blue theme
- **Forest**: Nature-inspired green theme
- **Sunset**: Warm sunset-inspired theme

## Configuration

Configuration is stored in `~/.arien-ai/config.yaml`:

```yaml
debug: false
default_provider: "openai"
default_model: "gpt-4"
default_theme: "default"
system_prompt: "You are a helpful AI assistant with access to various tools and functions."

providers:
  openai:
    base_url: "https://api.openai.com/v1"
    models: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"]
    default_model: "gpt-4"
  # ... other providers
```

Credentials are stored securely in `~/.arien-ai/credentials.json`.

## Development

### Project Structure
```
arien-ai-cli/
├── cmd/                    # CLI commands
├── internal/
│   ├── auth/              # Authentication management
│   ├── config/            # Configuration system
│   ├── llm/               # LLM provider integrations
│   ├── tools/             # Function calling tools
│   ├── ui/                # Terminal UI components
│   └── logger/            # Logging system
├── main.go                # Application entry point
├── go.mod                 # Go module definition
└── README.md              # This file
```

### Adding New Tools

1. Implement the `Tool` interface:
```go
type MyTool struct {
    *BaseTool
}

func (t *MyTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
    // Implementation
}
```

2. Register in `tools/manager.go`:
```go
m.registry.Register(NewMyTool())
```

### Adding New LLM Providers

1. Implement the `Provider` interface in `internal/llm/`
2. Add provider initialization in `llm/manager.go`
3. Update authentication in `internal/auth/`

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For issues and questions, please open an issue on the repository.
