/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"

	"arien-ai-cli/internal/auth"
	"arien-ai-cli/internal/ui"
)

var cfgFile string

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "arien-ai",
	Short: "AI-powered CLI terminal tool with LLM integrations",
	Long: `Arien AI CLI is a comprehensive AI-powered terminal tool that allows users to 
communicate with Large Language Models (LLMs) with function calling capabilities.

Supports multiple LLM providers:
- DeepSeek (deepseek-chat, deepseek-reasoner)
- OpenAI (GPT models)
- Google Gemini (OAuth and API key)
- Anthropic (Claude models)`,
	Run: func(cmd *cobra.Command, args []string) {
		// Check if user is authenticated
		if !auth.IsAuthenticated() {
			// Show authentication screen
			if err := auth.ShowAuthScreen(); err != nil {
				fmt.Fprintf(os.Stderr, "Authentication failed: %v\n", err)
				os.Exit(1)
			}
		}

		// Start interactive mode
		if err := ui.StartInteractiveMode(); err != nil {
			fmt.Fprintf(os.Stderr, "Error starting interactive mode: %v\n", err)
			os.Exit(1)
		}
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	cobra.OnInitialize(initConfig)

	// Global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is $HOME/.arien-ai.yaml)")
	rootCmd.PersistentFlags().Bool("debug", false, "enable debug mode")
	
	// Bind flags to viper
	viper.BindPFlag("debug", rootCmd.PersistentFlags().Lookup("debug"))
}

// initConfig reads in config file and ENV variables if set.
func initConfig() {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		home, err := os.UserHomeDir()
		cobra.CheckErr(err)

		viper.AddConfigPath(home)
		viper.SetConfigType("yaml")
		viper.SetConfigName(".arien-ai")
	}

	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err == nil {
		if viper.GetBool("debug") {
			fmt.Fprintln(os.Stderr, "Using config file:", viper.ConfigFileUsed())
		}
	}
}
