/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package config

import (
	"os"
	"path/filepath"
	"testing"
)

func TestInitialize(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Test initialization
	err = Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Check if config was created
	if AppConfig == nil {
		t.Fatal("AppConfig is nil after initialization")
	}

	// Check default values
	if AppConfig.DefaultProvider == "" {
		t.Error("DefaultProvider should not be empty")
	}

	if AppConfig.SystemPrompt == "" {
		t.Error("SystemPrompt should not be empty")
	}
}

func TestGetConfigDir(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory (works on Unix-like systems)
	originalHome := os.Getenv("HOME")
	originalUserProfile := os.Getenv("USERPROFILE")

	os.Setenv("HOME", tempDir)
	os.Setenv("USERPROFILE", tempDir) // For Windows

	defer func() {
		os.Setenv("HOME", originalHome)
		os.Setenv("USERPROFILE", originalUserProfile)
	}()

	configDir, err := getConfigDir()
	if err != nil {
		t.Fatalf("getConfigDir failed: %v", err)
	}

	expectedDir := filepath.Join(tempDir, ".arien-ai")
	if configDir != expectedDir {
		t.Errorf("Expected config dir %s, got %s", expectedDir, configDir)
	}
}

func TestSave(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Initialize config
	err = Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Modify config
	AppConfig.DefaultProvider = "test-provider"

	// Save config
	err = Save()
	if err != nil {
		t.Fatalf("Save failed: %v", err)
	}

	// Verify config file exists
	configDir, _ := getConfigDir()
	configFile := filepath.Join(configDir, "config.yaml")
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		t.Error("Config file was not created")
	}
}
