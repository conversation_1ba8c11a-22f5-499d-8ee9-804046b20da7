/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"encoding/json"
)

// Tool represents a function tool that can be called by LLMs
type Tool interface {
	// GetName returns the tool name
	GetName() string
	
	// GetDescription returns the tool description
	GetDescription() string
	
	// GetParameters returns the JSON schema for the tool parameters
	GetParameters() map[string]interface{}
	
	// Execute executes the tool with the given arguments
	Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error)
}

// ToolResult represents the result of a tool execution
type ToolResult struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// ToolRegistry manages available tools
type ToolRegistry struct {
	tools map[string]Tool
}

// NewToolRegistry creates a new tool registry
func NewToolRegistry() *ToolRegistry {
	return &ToolRegistry{
		tools: make(map[string]Tool),
	}
}

// Register registers a new tool
func (r *ToolRegistry) Register(tool Tool) {
	r.tools[tool.GetName()] = tool
}

// Get returns a tool by name
func (r *ToolRegistry) Get(name string) (Tool, bool) {
	tool, exists := r.tools[name]
	return tool, exists
}

// List returns all registered tools
func (r *ToolRegistry) List() []Tool {
	var tools []Tool
	for _, tool := range r.tools {
		tools = append(tools, tool)
	}
	return tools
}

// GetToolDefinitions returns tool definitions in LLM format
func (r *ToolRegistry) GetToolDefinitions() []ToolDefinition {
	var definitions []ToolDefinition
	for _, tool := range r.tools {
		definitions = append(definitions, ToolDefinition{
			Type: "function",
			Function: FunctionDefinition{
				Name:        tool.GetName(),
				Description: tool.GetDescription(),
				Parameters:  tool.GetParameters(),
			},
		})
	}
	return definitions
}

// Execute executes a tool by name with the given arguments
func (r *ToolRegistry) Execute(ctx context.Context, name string, argsJSON string) (*ToolResult, error) {
	tool, exists := r.Get(name)
	if !exists {
		return &ToolResult{
			Success: false,
			Error:   "Tool not found: " + name,
		}, nil
	}

	// Parse arguments
	var args map[string]interface{}
	if argsJSON != "" {
		if err := json.Unmarshal([]byte(argsJSON), &args); err != nil {
			return &ToolResult{
				Success: false,
				Error:   "Invalid arguments JSON: " + err.Error(),
			}, nil
		}
	}

	// Execute tool
	return tool.Execute(ctx, args)
}

// ToolDefinition represents a tool definition for LLMs
type ToolDefinition struct {
	Type     string             `json:"type"`
	Function FunctionDefinition `json:"function"`
}

// FunctionDefinition represents a function definition
type FunctionDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// BaseTool provides common functionality for tools
type BaseTool struct {
	name        string
	description string
	parameters  map[string]interface{}
}

// NewBaseTool creates a new base tool
func NewBaseTool(name, description string, parameters map[string]interface{}) *BaseTool {
	return &BaseTool{
		name:        name,
		description: description,
		parameters:  parameters,
	}
}

// GetName returns the tool name
func (t *BaseTool) GetName() string {
	return t.name
}

// GetDescription returns the tool description
func (t *BaseTool) GetDescription() string {
	return t.description
}

// GetParameters returns the tool parameters
func (t *BaseTool) GetParameters() map[string]interface{} {
	return t.parameters
}
