/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

import (
	"context"
	"fmt"
	"time"

	"arien-ai-cli/internal/auth"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/logger"
)

// Manager manages LLM providers and routing
type Manager struct {
	providers map[string]Provider
}

// NewManager creates a new LLM manager
func NewManager() *Manager {
	return &Manager{
		providers: make(map[string]Provider),
	}
}

// Initialize sets up all available providers based on authentication
func (m *Manager) Initialize() error {
	logger.Info("Initializing LLM providers")

	// Initialize OpenAI provider
	if cred, ok := auth.GetCredentials("openai"); ok {
		config := ProviderConfig{
			APIKey:      cred.APIKey,
			BaseURL:     "https://api.openai.com/v1",
			Temperature: 0.7,
			MaxTokens:   4096,
			Timeout:     30 * time.Second,
		}
		
		provider := NewOpenAIProvider(config)
		m.providers["openai"] = provider
		logger.Info("OpenAI provider initialized")
	}

	// Initialize DeepSeek provider
	if cred, ok := auth.GetCredentials("deepseek"); ok {
		config := ProviderConfig{
			APIKey:      cred.APIKey,
			BaseURL:     "https://api.deepseek.com/v1",
			Temperature: 0.7,
			MaxTokens:   4096,
			Timeout:     30 * time.Second,
		}
		
		provider := NewDeepSeekProvider(config)
		m.providers["deepseek"] = provider
		logger.Info("DeepSeek provider initialized")
	}

	// Initialize Anthropic provider
	if cred, ok := auth.GetCredentials("anthropic"); ok {
		config := ProviderConfig{
			APIKey:      cred.APIKey,
			BaseURL:     "https://api.anthropic.com/v1",
			Temperature: 0.7,
			MaxTokens:   4096,
			Timeout:     30 * time.Second,
		}
		
		provider := NewAnthropicProvider(config)
		m.providers["anthropic"] = provider
		logger.Info("Anthropic provider initialized")
	}

	// Initialize Gemini provider
	if cred, ok := auth.GetCredentials("gemini"); ok {
		config := ProviderConfig{
			APIKey:      cred.APIKey,
			BaseURL:     "https://generativelanguage.googleapis.com/v1",
			Temperature: 0.7,
			MaxTokens:   4096,
			Timeout:     30 * time.Second,
		}
		
		provider, err := NewGeminiProvider(config)
		if err != nil {
			logger.Error("Failed to initialize Gemini provider:", err)
		} else {
			m.providers["gemini"] = provider
			logger.Info("Gemini provider initialized")
		}
	}

	if len(m.providers) == 0 {
		return fmt.Errorf("no LLM providers are authenticated")
	}

	logger.Info(fmt.Sprintf("Initialized %d LLM providers", len(m.providers)))
	return nil
}

// GetProvider returns a specific provider
func (m *Manager) GetProvider(name string) (Provider, error) {
	provider, exists := m.providers[name]
	if !exists {
		return nil, fmt.Errorf("provider %s not found or not authenticated", name)
	}
	return provider, nil
}

// GetDefaultProvider returns the default provider based on configuration
func (m *Manager) GetDefaultProvider() (Provider, error) {
	defaultProvider := config.AppConfig.DefaultProvider
	if defaultProvider == "" {
		// Return the first available provider
		for _, provider := range m.providers {
			return provider, nil
		}
		return nil, fmt.Errorf("no providers available")
	}

	return m.GetProvider(defaultProvider)
}

// ListProviders returns all available provider names
func (m *Manager) ListProviders() []string {
	var names []string
	for name := range m.providers {
		names = append(names, name)
	}
	return names
}

// Chat sends a chat request using the specified provider
func (m *Manager) Chat(ctx context.Context, providerName string, request ChatRequest) (*ChatResponse, error) {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return nil, err
	}

	// Set default model if not specified
	if request.Model == "" {
		models := provider.GetModels()
		if len(models) > 0 {
			request.Model = models[0]
		}
	}

	// Set default temperature if not specified
	if request.Temperature == 0 {
		request.Temperature = 0.7
	}

	// Set default max tokens if not specified
	if request.MaxTokens == 0 {
		request.MaxTokens = 4096
	}

	return provider.Chat(ctx, request)
}

// Stream sends a streaming chat request using the specified provider
func (m *Manager) Stream(ctx context.Context, providerName string, request ChatRequest) (<-chan StreamResponse, error) {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return nil, err
	}

	// Set default model if not specified
	if request.Model == "" {
		models := provider.GetModels()
		if len(models) > 0 {
			request.Model = models[0]
		}
	}

	// Set default temperature if not specified
	if request.Temperature == 0 {
		request.Temperature = 0.7
	}

	// Set default max tokens if not specified
	if request.MaxTokens == 0 {
		request.MaxTokens = 4096
	}

	return provider.Stream(ctx, request)
}

// GetModels returns available models for a provider
func (m *Manager) GetModels(providerName string) ([]string, error) {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return nil, err
	}
	return provider.GetModels(), nil
}

// SupportsTools checks if a provider supports function calling
func (m *Manager) SupportsTools(providerName string) bool {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return false
	}
	return provider.SupportsTools()
}

// Close closes all providers
func (m *Manager) Close() error {
	for name, provider := range m.providers {
		if closer, ok := provider.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				logger.Error(fmt.Sprintf("Error closing provider %s: %v", name, err))
			}
		}
	}
	return nil
}
