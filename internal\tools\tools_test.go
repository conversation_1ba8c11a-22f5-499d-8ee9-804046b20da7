/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"testing"
)

func TestToolRegistry(t *testing.T) {
	registry := NewToolRegistry()

	// Test empty registry
	tools := registry.List()
	if len(tools) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected 0 tools, got %d", len(tools))
	}

	// Register a tool
	testTool := NewSystemInfoTool()
	registry.Register(testTool)

	// Test registry with one tool
	tools = registry.List()
	if len(tools) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 tool, got %d", len(tools))
	}

	// Test getting tool by name
	tool, exists := registry.Get("get_system_info")
	if !exists {
		t.Error("Tool should exist")
	}

	if tool.GetName() != "get_system_info" {
		t.Errorf("Expected tool name 'get_system_info', got '%s'", tool.GetName())
	}

	// Test getting non-existent tool
	_, exists = registry.Get("non_existent")
	if exists {
		t.<PERSON>rror("Non-existent tool should not exist")
	}
}

func TestSystemInfoTool(t *testing.T) {
	tool := NewSystemInfoTool()

	// Test tool properties
	if tool.GetName() != "get_system_info" {
		t.Errorf("Expected name 'get_system_info', got '%s'", tool.GetName())
	}

	if tool.GetDescription() == "" {
		t.Error("Description should not be empty")
	}

	params := tool.GetParameters()
	if params == nil {
		t.Error("Parameters should not be nil")
	}

	// Test execution with valid args
	ctx := context.Background()
	args := map[string]interface{}{
		"info_type": "os",
	}

	result, err := tool.Execute(ctx, args)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected success, got error: %s", result.Error)
	}

	if result.Data == nil {
		t.Error("Result data should not be nil")
	}

	// Test execution with invalid args
	invalidArgs := map[string]interface{}{
		"info_type": "invalid",
	}

	result, err = tool.Execute(ctx, invalidArgs)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if result.Success {
		t.Error("Expected failure with invalid args")
	}
}

func TestCalculatorTool(t *testing.T) {
	tool := NewCalculatorTool()

	ctx := context.Background()

	// Test addition
	args := map[string]interface{}{
		"operation": "add",
		"operands":  []interface{}{2.0, 3.0},
	}

	result, err := tool.Execute(ctx, args)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected success, got error: %s", result.Error)
	}

	data, ok := result.Data.(map[string]interface{})
	if !ok {
		t.Fatal("Result data should be a map")
	}

	resultValue, ok := data["result"].(float64)
	if !ok {
		t.Fatal("Result should be a float64")
	}

	if resultValue != 5.0 {
		t.Errorf("Expected result 5.0, got %f", resultValue)
	}

	// Test division by zero
	args = map[string]interface{}{
		"operation": "divide",
		"operands":  []interface{}{5.0, 0.0},
	}

	result, err = tool.Execute(ctx, args)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if result.Success {
		t.Error("Expected failure for division by zero")
	}
}

func TestTextTool(t *testing.T) {
	tool := NewTextTool()

	ctx := context.Background()

	// Test uppercase
	args := map[string]interface{}{
		"operation": "uppercase",
		"text":      "hello world",
	}

	result, err := tool.Execute(ctx, args)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected success, got error: %s", result.Error)
	}

	data, ok := result.Data.(map[string]interface{})
	if !ok {
		t.Fatal("Result data should be a map")
	}

	resultValue, ok := data["result"].(string)
	if !ok {
		t.Fatal("Result should be a string")
	}

	if resultValue != "HELLO WORLD" {
		t.Errorf("Expected 'HELLO WORLD', got '%s'", resultValue)
	}

	// Test length
	args = map[string]interface{}{
		"operation": "length",
		"text":      "hello",
	}

	result, err = tool.Execute(ctx, args)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected success, got error: %s", result.Error)
	}

	data, ok = result.Data.(map[string]interface{})
	if !ok {
		t.Fatal("Result data should be a map")
	}

	length, ok := data["result"].(int)
	if !ok {
		t.Fatal("Result should be an int")
	}

	if length != 5 {
		t.Errorf("Expected length 5, got %d", length)
	}
}

func TestTimeTool(t *testing.T) {
	tool := NewTimeTool()

	ctx := context.Background()

	// Test default format
	args := map[string]interface{}{}

	result, err := tool.Execute(ctx, args)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected success, got error: %s", result.Error)
	}

	data, ok := result.Data.(map[string]interface{})
	if !ok {
		t.Fatal("Result data should be a map")
	}

	timeStr, ok := data["time"].(string)
	if !ok {
		t.Fatal("Time should be a string")
	}

	if timeStr == "" {
		t.Error("Time string should not be empty")
	}

	// Test unix format
	args = map[string]interface{}{
		"format": "unix",
	}

	result, err = tool.Execute(ctx, args)
	if err != nil {
		t.Fatalf("Execute failed: %v", err)
	}

	if !result.Success {
		t.Errorf("Expected success, got error: %s", result.Error)
	}
}

func TestToolManager(t *testing.T) {
	manager := NewManager()

	// Test initialization
	err := manager.Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Test getting registry
	registry := manager.GetRegistry()
	if registry == nil {
		t.Error("Registry should not be nil")
	}

	// Test listing tools
	tools := manager.ListTools()
	if len(tools) == 0 {
		t.Error("Should have tools after initialization")
	}

	// Test getting tool definitions
	definitions := manager.GetToolDefinitions()
	if len(definitions) == 0 {
		t.Error("Should have tool definitions after initialization")
	}

	// Test getting tool by name
	tool, exists := manager.GetToolByName("get_system_info")
	if !exists {
		t.Error("Should have system info tool")
	}

	if tool.GetName() != "get_system_info" {
		t.Errorf("Expected 'get_system_info', got '%s'", tool.GetName())
	}
}
