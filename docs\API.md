# Arien AI CLI API Documentation

This document provides detailed API documentation for the Arien AI CLI components.

## Table of Contents

- [Configuration](#configuration)
- [Authentication](#authentication)
- [LLM Providers](#llm-providers)
- [Tools System](#tools-system)
- [UI Components](#ui-components)

## Configuration

### Package: `internal/config`

The configuration system manages application settings and provider configurations.

#### Types

```go
type Config struct {
    Debug           bool                      `mapstructure:"debug"`
    DefaultProvider string                    `mapstructure:"default_provider"`
    DefaultModel    string                    `mapstructure:"default_model"`
    DefaultTheme    string                    `mapstructure:"default_theme"`
    SystemPrompt    string                    `mapstructure:"system_prompt"`
    Providers       map[string]ProviderConfig `mapstructure:"providers"`
}

type ProviderConfig struct {
    APIKey       string            `mapstructure:"api_key"`
    BaseURL      string            `mapstructure:"base_url"`
    Models       []string          `mapstructure:"models"`
    DefaultModel string            `mapstructure:"default_model"`
    Headers      map[string]string `mapstructure:"headers"`
    OAuth        OAuthConfig       `mapstructure:"oauth"`
}
```

#### Functions

- `Initialize() error` - Initialize configuration system
- `Save() error` - Save current configuration to file
- `GetConfigDir() (string, error)` - Get configuration directory path

## Authentication

### Package: `internal/auth`

The authentication system manages API keys and OAuth tokens for LLM providers.

#### Types

```go
type Credentials struct {
    Providers map[string]ProviderCredentials `json:"providers"`
}

type ProviderCredentials struct {
    APIKey        string `json:"api_key,omitempty"`
    AccessToken   string `json:"access_token,omitempty"`
    RefreshToken  string `json:"refresh_token,omitempty"`
    ExpiresAt     int64  `json:"expires_at,omitempty"`
    Authenticated bool   `json:"authenticated"`
}
```

#### Functions

- `Initialize() error` - Initialize authentication system
- `IsAuthenticated() bool` - Check if any provider is authenticated
- `IsProviderAuthenticated(provider string) bool` - Check specific provider
- `ShowAuthScreen() error` - Display interactive authentication screen
- `Logout(provider string) error` - Logout from provider(s)
- `ShowStatus()` - Display authentication status
- `GetCredentials(provider string) (ProviderCredentials, bool)` - Get provider credentials

## LLM Providers

### Package: `internal/llm`

The LLM system provides unified interface for multiple AI providers.

#### Interfaces

```go
type Provider interface {
    Chat(ctx context.Context, request ChatRequest) (*ChatResponse, error)
    Stream(ctx context.Context, request ChatRequest) (<-chan StreamResponse, error)
    GetModels() []string
    GetName() string
    SupportsTools() bool
}
```

#### Types

```go
type ChatRequest struct {
    Model       string    `json:"model"`
    Messages    []Message `json:"messages"`
    Tools       []Tool    `json:"tools,omitempty"`
    ToolChoice  string    `json:"tool_choice,omitempty"`
    Temperature float64   `json:"temperature,omitempty"`
    MaxTokens   int       `json:"max_tokens,omitempty"`
    Stream      bool      `json:"stream,omitempty"`
}

type ChatResponse struct {
    ID      string   `json:"id"`
    Object  string   `json:"object"`
    Created int64    `json:"created"`
    Model   string   `json:"model"`
    Choices []Choice `json:"choices"`
    Usage   Usage    `json:"usage"`
}

type Message struct {
    Role       string     `json:"role"`
    Content    string     `json:"content"`
    Name       string     `json:"name,omitempty"`
    ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
    ToolCallID string     `json:"tool_call_id,omitempty"`
}
```

#### Manager Functions

- `NewManager() *Manager` - Create new LLM manager
- `Initialize() error` - Initialize all authenticated providers
- `GetProvider(name string) (Provider, error)` - Get specific provider
- `GetDefaultProvider() (Provider, error)` - Get default provider
- `ListProviders() []string` - List available providers
- `Chat(ctx, provider, request) (*ChatResponse, error)` - Send chat request
- `Stream(ctx, provider, request) (<-chan StreamResponse, error)` - Send streaming request

### Supported Providers

#### OpenAI
- Models: GPT-4, GPT-4 Turbo, GPT-3.5 Turbo
- Function calling: ✅ Supported
- Streaming: ✅ Supported

#### DeepSeek
- Models: deepseek-chat, deepseek-reasoner, deepseek-coder
- Function calling: ✅ Supported
- Streaming: ✅ Supported

#### Anthropic Claude
- Models: Claude-3 Opus, Claude-3 Sonnet, Claude-3 Haiku
- Function calling: ✅ Supported
- Streaming: ⚠️ Basic implementation

#### Google Gemini
- Models: Gemini Pro, Gemini Pro Vision, Gemini 1.5 Pro
- Function calling: ✅ Supported
- Streaming: ✅ Supported

## Tools System

### Package: `internal/tools`

The tools system provides function calling capabilities for LLMs.

#### Interfaces

```go
type Tool interface {
    GetName() string
    GetDescription() string
    GetParameters() map[string]interface{}
    Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error)
}
```

#### Types

```go
type ToolResult struct {
    Success bool        `json:"success"`
    Data    interface{} `json:"data,omitempty"`
    Error   string      `json:"error,omitempty"`
    Message string      `json:"message,omitempty"`
}

type ToolRegistry struct {
    tools map[string]Tool
}
```

#### Manager Functions

- `NewManager() *Manager` - Create new tools manager
- `Initialize() error` - Initialize and register all tools
- `ExecuteTool(ctx, name, argsJSON) (*ToolResult, error)` - Execute tool
- `GetToolDefinitions() []ToolDefinition` - Get LLM-compatible tool definitions
- `ListTools() []ToolInfo` - List available tools
- `RegisterTool(tool Tool)` - Register new tool

### Available Tools

#### System Tools
- **get_system_info**: Get OS, CPU, memory, disk information
- **execute_command**: Execute system commands with timeout
- **get_current_time**: Get current date/time in various formats

#### File Tools
- **read_file**: Read file contents
- **write_file**: Write content to files
- **list_directory**: List directory contents

#### Utility Tools
- **calculate**: Mathematical calculations
- **text_operation**: Text manipulation operations
- **network_operation**: Network operations (ping, HTTP)

## UI Components

### Package: `internal/ui`

The UI system provides terminal interface components using Bubbletea.

#### Themes

```go
type Theme struct {
    Name        string
    Description string
    Primary     lipgloss.Color
    Secondary   lipgloss.Color
    Accent      lipgloss.Color
    Background  lipgloss.Color
    Foreground  lipgloss.Color
    Success     lipgloss.Color
    Warning     lipgloss.Color
    Error       lipgloss.Color
    Muted       lipgloss.Color
    Border      lipgloss.Color
}
```

#### Functions

- `InitializeThemes()` - Initialize theme system
- `SetTheme(name string) bool` - Set current theme
- `GetTheme() Theme` - Get current theme
- `GetAvailableThemes() map[string]Theme` - Get all themes
- `StartInteractiveMode() error` - Start main chat interface
- `StartChat(provider, model, theme string) error` - Start chat with parameters
- `ShowThemeSelector() error` - Show theme selection interface

#### Available Themes
- **default**: Clean and modern
- **dark**: Dark theme for low-light
- **cyberpunk**: Neon-inspired
- **ocean**: Blue ocean theme
- **forest**: Green nature theme
- **sunset**: Warm sunset colors

## Error Handling

All functions return errors following Go conventions. Common error types:

- Configuration errors: Invalid config files, missing directories
- Authentication errors: Invalid API keys, network issues
- LLM errors: API failures, rate limits, invalid requests
- Tool errors: Execution failures, invalid arguments
- UI errors: Terminal issues, theme problems

## Examples

See `examples/basic_usage.go` for comprehensive usage examples.

## Testing

Run tests with:
```bash
go test ./...
```

Run with coverage:
```bash
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```
