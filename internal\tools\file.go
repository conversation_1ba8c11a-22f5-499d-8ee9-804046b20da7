/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
)

// FileReadTool reads file contents
type FileReadTool struct {
	*BaseTool
}

// NewFileReadTool creates a new file read tool
func NewFileReadTool() *FileReadTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"path": map[string]interface{}{
				"type":        "string",
				"description": "Path to the file to read",
			},
			"encoding": map[string]interface{}{
				"type":        "string",
				"description": "File encoding (default: utf-8)",
				"enum":        []string{"utf-8", "ascii"},
			},
		},
		"required": []string{"path"},
	}

	return &FileReadTool{
		BaseTool: NewBaseTool(
			"read_file",
			"Read the contents of a file",
			parameters,
		),
	}
}

// Execute executes the file read tool
func (t *FileReadTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	path, ok := args["path"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "path parameter is required and must be a string",
		}, nil
	}

	// Security check - prevent reading sensitive files
	if t.isRestrictedPath(path) {
		return &ToolResult{
			Success: false,
			Error:   "Access to this file is restricted for security reasons",
		}, nil
	}

	content, err := os.ReadFile(path)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to read file: %v", err),
		}, nil
	}

	// Get file info
	info, err := os.Stat(path)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to get file info: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"content":  string(content),
			"size":     info.Size(),
			"modified": info.ModTime(),
			"path":     path,
		},
		Message: fmt.Sprintf("Successfully read file: %s (%d bytes)", path, len(content)),
	}, nil
}

func (t *FileReadTool) isRestrictedPath(path string) bool {
	restrictedPaths := []string{
		"/etc/passwd",
		"/etc/shadow",
		"/etc/hosts",
		"~/.ssh",
		"~/.aws",
		"~/.config",
	}

	for _, restricted := range restrictedPaths {
		if strings.Contains(path, restricted) {
			return true
		}
	}
	return false
}

// FileWriteTool writes content to files
type FileWriteTool struct {
	*BaseTool
}

// NewFileWriteTool creates a new file write tool
func NewFileWriteTool() *FileWriteTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"path": map[string]interface{}{
				"type":        "string",
				"description": "Path to the file to write",
			},
			"content": map[string]interface{}{
				"type":        "string",
				"description": "Content to write to the file",
			},
			"mode": map[string]interface{}{
				"type":        "string",
				"description": "Write mode: 'write' (overwrite) or 'append'",
				"enum":        []string{"write", "append"},
			},
		},
		"required": []string{"path", "content"},
	}

	return &FileWriteTool{
		BaseTool: NewBaseTool(
			"write_file",
			"Write content to a file",
			parameters,
		),
	}
}

// Execute executes the file write tool
func (t *FileWriteTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	path, ok := args["path"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "path parameter is required and must be a string",
		}, nil
	}

	content, ok := args["content"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "content parameter is required and must be a string",
		}, nil
	}

	mode := "write"
	if modeInterface, exists := args["mode"]; exists {
		if modeStr, ok := modeInterface.(string); ok {
			mode = modeStr
		}
	}

	// Security check
	if t.isRestrictedPath(path) {
		return &ToolResult{
			Success: false,
			Error:   "Writing to this path is restricted for security reasons",
		}, nil
	}

	var err error
	var bytesWritten int

	switch mode {
	case "write":
		err = os.WriteFile(path, []byte(content), 0644)
		bytesWritten = len(content)
	case "append":
		file, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return &ToolResult{
				Success: false,
				Error:   fmt.Sprintf("Failed to open file for append: %v", err),
			}, nil
		}
		defer file.Close()

		n, err := file.WriteString(content)
		bytesWritten = n
	}

	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to write file: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"path":          path,
			"bytes_written": bytesWritten,
			"mode":          mode,
		},
		Message: fmt.Sprintf("Successfully wrote %d bytes to %s", bytesWritten, path),
	}, nil
}

func (t *FileWriteTool) isRestrictedPath(path string) bool {
	restrictedPaths := []string{
		"/etc/",
		"/usr/",
		"/bin/",
		"/sbin/",
		"/boot/",
		"/sys/",
		"/proc/",
	}

	for _, restricted := range restrictedPaths {
		if strings.HasPrefix(path, restricted) {
			return true
		}
	}
	return false
}

// DirectoryListTool lists directory contents
type DirectoryListTool struct {
	*BaseTool
}

// NewDirectoryListTool creates a new directory list tool
func NewDirectoryListTool() *DirectoryListTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"path": map[string]interface{}{
				"type":        "string",
				"description": "Path to the directory to list",
			},
			"recursive": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to list recursively (default: false)",
			},
			"show_hidden": map[string]interface{}{
				"type":        "boolean",
				"description": "Whether to show hidden files (default: false)",
			},
		},
		"required": []string{"path"},
	}

	return &DirectoryListTool{
		BaseTool: NewBaseTool(
			"list_directory",
			"List the contents of a directory",
			parameters,
		),
	}
}

// Execute executes the directory list tool
func (t *DirectoryListTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	path, ok := args["path"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "path parameter is required and must be a string",
		}, nil
	}

	recursive := false
	if recursiveInterface, exists := args["recursive"]; exists {
		if recursiveBool, ok := recursiveInterface.(bool); ok {
			recursive = recursiveBool
		}
	}

	showHidden := false
	if showHiddenInterface, exists := args["show_hidden"]; exists {
		if showHiddenBool, ok := showHiddenInterface.(bool); ok {
			showHidden = showHiddenBool
		}
	}

	var files []map[string]interface{}
	var err error

	if recursive {
		err = filepath.WalkDir(path, func(filePath string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}

			if !showHidden && strings.HasPrefix(d.Name(), ".") {
				if d.IsDir() {
					return filepath.SkipDir
				}
				return nil
			}

			info, err := d.Info()
			if err != nil {
				return err
			}

			files = append(files, map[string]interface{}{
				"name":     d.Name(),
				"path":     filePath,
				"is_dir":   d.IsDir(),
				"size":     info.Size(),
				"modified": info.ModTime(),
				"mode":     info.Mode().String(),
			})

			return nil
		})
	} else {
		entries, err := os.ReadDir(path)
		if err != nil {
			return &ToolResult{
				Success: false,
				Error:   fmt.Sprintf("Failed to read directory: %v", err),
			}, nil
		}

		for _, entry := range entries {
			if !showHidden && strings.HasPrefix(entry.Name(), ".") {
				continue
			}

			info, err := entry.Info()
			if err != nil {
				continue
			}

			files = append(files, map[string]interface{}{
				"name":     entry.Name(),
				"path":     filepath.Join(path, entry.Name()),
				"is_dir":   entry.IsDir(),
				"size":     info.Size(),
				"modified": info.ModTime(),
				"mode":     info.Mode().String(),
			})
		}
	}

	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to list directory: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"path":      path,
			"files":     files,
			"count":     len(files),
			"recursive": recursive,
		},
		Message: fmt.Sprintf("Listed %d items in %s", len(files), path),
	}, nil
}
