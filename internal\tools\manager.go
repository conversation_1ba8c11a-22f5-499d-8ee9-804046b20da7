/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"

	"arien-ai-cli/internal/logger"
)

// Manager manages the tool registry and execution
type Manager struct {
	registry *ToolRegistry
}

// NewManager creates a new tool manager
func NewManager() *Manager {
	return &Manager{
		registry: NewToolRegistry(),
	}
}

// Initialize sets up all available tools
func (m *Manager) Initialize() error {
	logger.Info("Initializing tool registry")

	// Register system tools
	m.registry.Register(NewSystemInfoTool())
	m.registry.Register(NewCommandTool())
	m.registry.Register(NewTimeTool())

	// Register file tools
	m.registry.Register(NewFileReadTool())
	m.registry.Register(NewFileWriteTool())
	m.registry.Register(NewDirectoryListTool())

	// Register utility tools
	m.registry.Register(NewCalculatorTool())
	m.registry.Register(NewTextTool())
	m.registry.Register(NewNetworkTool())

	logger.Info(fmt.Sprintf("Registered %d tools", len(m.registry.List())))
	return nil
}

// GetRegistry returns the tool registry
func (m *Manager) GetRegistry() *ToolRegistry {
	return m.registry
}

// ExecuteTool executes a tool by name with arguments
func (m *Manager) ExecuteTool(ctx context.Context, name string, argsJSON string) (*ToolResult, error) {
	logger.Debug(fmt.Sprintf("Executing tool: %s with args: %s", name, argsJSON))
	
	result, err := m.registry.Execute(ctx, name, argsJSON)
	if err != nil {
		logger.Error(fmt.Sprintf("Tool execution error: %v", err))
		return nil, err
	}

	if result.Success {
		logger.Info(fmt.Sprintf("Tool %s executed successfully: %s", name, result.Message))
	} else {
		logger.Warning(fmt.Sprintf("Tool %s failed: %s", name, result.Error))
	}

	return result, nil
}

// GetToolDefinitions returns all tool definitions for LLM integration
func (m *Manager) GetToolDefinitions() []ToolDefinition {
	return m.registry.GetToolDefinitions()
}

// ListTools returns information about all available tools
func (m *Manager) ListTools() []ToolInfo {
	var tools []ToolInfo
	for _, tool := range m.registry.List() {
		tools = append(tools, ToolInfo{
			Name:        tool.GetName(),
			Description: tool.GetDescription(),
			Parameters:  tool.GetParameters(),
		})
	}
	return tools
}

// ToolInfo represents information about a tool
type ToolInfo struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// GetToolByName returns a tool by name
func (m *Manager) GetToolByName(name string) (Tool, bool) {
	return m.registry.Get(name)
}

// RegisterTool registers a new tool
func (m *Manager) RegisterTool(tool Tool) {
	m.registry.Register(tool)
	logger.Info(fmt.Sprintf("Registered new tool: %s", tool.GetName()))
}
