/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

import (
	"context"
	"fmt"

	"github.com/sashabaranov/go-openai"
	"arien-ai-cli/internal/logger"
)

// DeepSeekProvider implements the Provider interface for DeepSeek
type DeepSeekProvider struct {
	client *openai.Client
	config ProviderConfig
}

// NewDeepSeekProvider creates a new DeepSeek provider
func NewDeepSeekProvider(config ProviderConfig) *DeepSeekProvider {
	clientConfig := openai.DefaultConfig(config.APIKey)
	clientConfig.BaseURL = "https://api.deepseek.com/v1"
	if config.BaseURL != "" {
		clientConfig.BaseURL = config.BaseURL
	}

	return &DeepSeekProvider{
		client: openai.NewClientWithConfig(clientConfig),
		config: config,
	}
}

// Chat sends a chat completion request to DeepSeek
func (p *DeepSeekProvider) Chat(ctx context.Context, request ChatRequest) (*ChatResponse, error) {
	logger.Debug("DeepSeek Chat request:", request)

	// Convert our request to OpenAI format (DeepSeek uses OpenAI-compatible API)
	req := openai.ChatCompletionRequest{
		Model:       request.Model,
		Messages:    convertMessagesToOpenAI(request.Messages),
		Temperature: float32(request.Temperature),
		MaxTokens:   request.MaxTokens,
	}

	// Add tools if provided and supported
	if len(request.Tools) > 0 && p.SupportsTools() {
		req.Tools = convertToolsToOpenAI(request.Tools)
		if request.ToolChoice != "" {
			req.ToolChoice = request.ToolChoice
		}
	}

	// Make the request
	resp, err := p.client.CreateChatCompletion(ctx, req)
	if err != nil {
		logger.Error("DeepSeek API error:", err)
		return nil, fmt.Errorf("DeepSeek API error: %w", err)
	}

	// Convert response back to our format
	return convertOpenAIResponse(resp), nil
}

// Stream sends a streaming chat completion request to DeepSeek
func (p *DeepSeekProvider) Stream(ctx context.Context, request ChatRequest) (<-chan StreamResponse, error) {
	logger.Debug("DeepSeek Stream request:", request)

	// Convert our request to OpenAI format
	req := openai.ChatCompletionRequest{
		Model:       request.Model,
		Messages:    convertMessagesToOpenAI(request.Messages),
		Temperature: float32(request.Temperature),
		MaxTokens:   request.MaxTokens,
		Stream:      true,
	}

	// Add tools if provided and supported
	if len(request.Tools) > 0 && p.SupportsTools() {
		req.Tools = convertToolsToOpenAI(request.Tools)
		if request.ToolChoice != "" {
			req.ToolChoice = request.ToolChoice
		}
	}

	// Create stream
	stream, err := p.client.CreateChatCompletionStream(ctx, req)
	if err != nil {
		logger.Error("DeepSeek Stream API error:", err)
		return nil, fmt.Errorf("DeepSeek Stream API error: %w", err)
	}

	// Create response channel
	responseChan := make(chan StreamResponse, 10)

	// Start goroutine to handle streaming
	go func() {
		defer close(responseChan)
		defer stream.Close()

		for {
			response, err := stream.Recv()
			if err != nil {
				if err.Error() == "EOF" {
					responseChan <- StreamResponse{Done: true}
					return
				}
				responseChan <- StreamResponse{Error: err, Done: true}
				return
			}

			// Convert and send response
			streamResp := convertOpenAIStreamResponse(response)
			responseChan <- streamResp
		}
	}()

	return responseChan, nil
}

// GetModels returns available DeepSeek models
func (p *DeepSeekProvider) GetModels() []string {
	return []string{
		"deepseek-chat",
		"deepseek-reasoner",
		"deepseek-coder",
	}
}

// GetName returns the provider name
func (p *DeepSeekProvider) GetName() string {
	return "deepseek"
}

// SupportsTools returns whether DeepSeek supports function calling
func (p *DeepSeekProvider) SupportsTools() bool {
	// DeepSeek supports function calling for certain models
	return true
}
