/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"testing"

	"github.com/charmbracelet/lipgloss"
)

func TestInitializeThemes(t *testing.T) {
	InitializeThemes()

	theme := GetTheme()
	if theme.Name == "" {
		t.<PERSON><PERSON>r("Theme name should not be empty after initialization")
	}

	if theme.Primary == "" {
		t.<PERSON>rror("Theme primary color should not be empty")
	}
}

func TestSetTheme(t *testing.T) {
	InitializeThemes()

	// Test setting valid theme
	success := SetTheme("dark")
	if !success {
		t.Error("Should successfully set dark theme")
	}

	theme := GetTheme()
	if theme.Name != "Dark" {
		t.<PERSON>rf("Expected theme name 'Dark', got '%s'", theme.Name)
	}

	// Test setting invalid theme
	success = SetTheme("non-existent")
	if success {
		t.<PERSON>rror("Should not successfully set non-existent theme")
	}

	// Theme should remain unchanged
	theme = GetTheme()
	if theme.Name != "Dark" {
		t.<PERSON>("Theme should remain 'Dark', got '%s'", theme.Name)
	}
}

func TestGetAvailableThemes(t *testing.T) {
	themes := GetAvailableThemes()

	if len(themes) == 0 {
		t.Error("Should have available themes")
	}

	// Check for expected themes
	expectedThemes := []string{"default", "dark", "cyberpunk", "ocean", "forest", "sunset"}
	for _, expected := range expectedThemes {
		if _, exists := themes[expected]; !exists {
			t.Errorf("Expected theme '%s' not found", expected)
		}
	}
}

func TestGetThemeNames(t *testing.T) {
	names := GetThemeNames()

	if len(names) == 0 {
		t.Error("Should have theme names")
	}

	// Check that all names correspond to actual themes
	themes := GetAvailableThemes()
	for _, name := range names {
		if _, exists := themes[name]; !exists {
			t.Errorf("Theme name '%s' does not correspond to actual theme", name)
		}
	}
}

func TestThemeProperties(t *testing.T) {
	themes := GetAvailableThemes()

	for name, theme := range themes {
		if theme.Name == "" {
			t.Errorf("Theme '%s' should have a name", name)
		}

		if theme.Description == "" {
			t.Errorf("Theme '%s' should have a description", name)
		}

		if theme.Primary == "" {
			t.Errorf("Theme '%s' should have a primary color", name)
		}

		if theme.Secondary == "" {
			t.Errorf("Theme '%s' should have a secondary color", name)
		}

		if theme.Background == "" {
			t.Errorf("Theme '%s' should have a background color", name)
		}

		if theme.Foreground == "" {
			t.Errorf("Theme '%s' should have a foreground color", name)
		}
	}
}

func TestStyleHelpers(t *testing.T) {
	InitializeThemes()
	SetTheme("default")

	// Test that style helpers return valid styles
	styleTests := []struct {
		name  string
		style lipgloss.Style
	}{
		{"Primary", PrimaryStyle()},
		{"Secondary", SecondaryStyle()},
		{"Accent", AccentStyle()},
		{"Success", SuccessStyle()},
		{"Warning", WarningStyle()},
		{"Error", ErrorStyle()},
		{"Muted", MutedStyle()},
		{"Border", BorderStyle()},
		{"Header", HeaderStyle()},
		{"Message", MessageStyle()},
		{"UserMessage", UserMessageStyle()},
		{"AssistantMessage", AssistantMessageStyle()},
		{"Input", InputStyle()},
		{"Status", StatusStyle()},
	}

	for _, test := range styleTests {
		// Test that the style can render text
		rendered := test.style.Render("test")
		if rendered == "" {
			t.Errorf("%s style returned empty render", test.name)
		}

		// For styles that should have colors, check they're not the same as unstyled
		if test.name != "Border" && test.name != "Input" {
			if rendered == "test" {
				// This might be expected for some styles, so just log it
				t.Logf("%s style renders the same as unstyled text", test.name)
			}
		}
	}
}

func TestThemeConsistency(t *testing.T) {
	themes := GetAvailableThemes()

	// Test that all themes have consistent structure
	for name, theme := range themes {
		// Test that colors are valid lipgloss colors
		colors := []lipgloss.Color{
			theme.Primary,
			theme.Secondary,
			theme.Accent,
			theme.Background,
			theme.Foreground,
			theme.Success,
			theme.Warning,
			theme.Error,
			theme.Muted,
			theme.Border,
		}

		for i, color := range colors {
			if color == "" {
				t.Errorf("Theme '%s' has empty color at index %d", name, i)
			}
		}
	}
}

func TestThemeSwitching(t *testing.T) {
	InitializeThemes()

	// Test switching between themes
	themeNames := GetThemeNames()
	
	for _, themeName := range themeNames {
		success := SetTheme(themeName)
		if !success {
			t.Errorf("Failed to set theme '%s'", themeName)
			continue
		}

		currentTheme := GetTheme()
		expectedTheme := GetAvailableThemes()[themeName]
		
		if currentTheme.Name != expectedTheme.Name {
			t.Errorf("Expected theme name '%s', got '%s'", expectedTheme.Name, currentTheme.Name)
		}

		if currentTheme.Primary != expectedTheme.Primary {
			t.Errorf("Theme '%s': expected primary color '%s', got '%s'", 
				themeName, expectedTheme.Primary, currentTheme.Primary)
		}
	}
}
