/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"arien-ai-cli/internal/config"
)

// ThemeSelectorModel represents the theme selector interface
type ThemeSelectorModel struct {
	themes   map[string]Theme
	selected int
	width    int
	height   int
	preview  bool
}

// NewThemeSelectorModel creates a new theme selector model
func NewThemeSelectorModel() *ThemeSelectorModel {
	return &ThemeSelectorModel{
		themes:   GetAvailableThemes(),
		selected: 0,
		preview:  true,
	}
}

// Init initializes the theme selector
func (m *ThemeSelectorModel) Init() tea.Cmd {
	return tea.EnterAltScreen
}

// Update handles messages and updates the model
func (m *ThemeSelectorModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "esc", "q":
			return m, tea.Quit

		case "up", "k":
			if m.selected > 0 {
				m.selected--
				if m.preview {
					m.applyPreviewTheme()
				}
			}

		case "down", "j":
			themeNames := GetThemeNames()
			if m.selected < len(themeNames)-1 {
				m.selected++
				if m.preview {
					m.applyPreviewTheme()
				}
			}

		case "enter", " ":
			return m, m.selectTheme()

		case "p":
			m.preview = !m.preview
			if m.preview {
				m.applyPreviewTheme()
			}
		}
	}

	return m, nil
}

// View renders the theme selector interface
func (m *ThemeSelectorModel) View() string {
	var b strings.Builder

	// Header
	header := HeaderStyle().Render("🎨 Theme Selector")
	b.WriteString(header)
	b.WriteString("\n\n")

	// Instructions
	instructions := MutedStyle().Render("Use ↑/↓ to navigate, Enter to select, 'p' to toggle preview, 'q' to quit")
	b.WriteString(instructions)
	b.WriteString("\n\n")

	// Theme list
	themeNames := GetThemeNames()
	for i, name := range themeNames {
		theme := m.themes[name]
		
		var style lipgloss.Style
		if i == m.selected {
			style = lipgloss.NewStyle().
				Foreground(theme.Primary).
				Background(theme.Background).
				Bold(true).
				Padding(0, 2).
				Border(lipgloss.RoundedBorder()).
				BorderForeground(theme.Accent)
		} else {
			style = lipgloss.NewStyle().
				Foreground(theme.Secondary).
				Padding(0, 2)
		}

		line := fmt.Sprintf("%s - %s", theme.Name, theme.Description)
		b.WriteString(style.Render(line))
		b.WriteString("\n")

		// Show color preview for selected theme
		if i == m.selected {
			colorPreview := m.renderColorPreview(theme)
			b.WriteString(colorPreview)
			b.WriteString("\n")
		}
	}

	// Live preview section
	if m.preview {
		b.WriteString("\n")
		b.WriteString(HeaderStyle().Render("🔍 Live Preview"))
		b.WriteString("\n")
		b.WriteString(m.renderLivePreview())
	}

	// Status
	b.WriteString("\n")
	status := fmt.Sprintf("Preview: %s | Selected: %d/%d", 
		map[bool]string{true: "ON", false: "OFF"}[m.preview],
		m.selected+1, 
		len(themeNames))
	b.WriteString(StatusStyle().Render(status))

	return b.String()
}

// renderColorPreview renders a color palette preview
func (m *ThemeSelectorModel) renderColorPreview(theme Theme) string {
	var parts []string

	colors := []struct {
		name  string
		color lipgloss.Color
	}{
		{"Primary", theme.Primary},
		{"Secondary", theme.Secondary},
		{"Accent", theme.Accent},
		{"Success", theme.Success},
		{"Warning", theme.Warning},
		{"Error", theme.Error},
	}

	for _, c := range colors {
		colorBlock := lipgloss.NewStyle().
			Background(c.color).
			Foreground(theme.Background).
			Padding(0, 1).
			Render(c.name)
		parts = append(parts, colorBlock)
	}

	return "  " + strings.Join(parts, " ")
}

// renderLivePreview renders a live preview of the theme
func (m *ThemeSelectorModel) renderLivePreview() string {
	var b strings.Builder

	// Sample chat interface
	b.WriteString(UserMessageStyle().Render("👤 [14:30] Hello, how are you?"))
	b.WriteString("\n")
	b.WriteString(AssistantMessageStyle().Render("🤖 [14:30] I'm doing great! How can I help you today?"))
	b.WriteString("\n")
	b.WriteString(AccentStyle().Render("🔧 Called: get_weather(location='New York')"))
	b.WriteString("\n")
	b.WriteString(SuccessStyle().Render("✅ Function executed successfully"))
	b.WriteString("\n")
	b.WriteString(WarningStyle().Render("⚠️  Rate limit warning"))
	b.WriteString("\n")
	b.WriteString(ErrorStyle().Render("❌ Connection error"))
	b.WriteString("\n")

	// Input preview
	inputPreview := PrimaryStyle().Render("💬 You: ") + 
		InputStyle().Render("Type your message here...")
	b.WriteString(inputPreview)

	return b.String()
}

// applyPreviewTheme applies the currently selected theme for preview
func (m *ThemeSelectorModel) applyPreviewTheme() {
	themeNames := GetThemeNames()
	if m.selected < len(themeNames) {
		SetTheme(themeNames[m.selected])
	}
}

// selectTheme selects the current theme and saves it
func (m *ThemeSelectorModel) selectTheme() tea.Cmd {
	themeNames := GetThemeNames()
	if m.selected < len(themeNames) {
		selectedTheme := themeNames[m.selected]
		SetTheme(selectedTheme)
		
		// Save to config
		config.AppConfig.DefaultTheme = selectedTheme
		config.Save()
	}
	
	return tea.Quit
}
