/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package main

import (
	"context"
	"fmt"
	"log"

	"arien-ai-cli/internal/auth"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/llm"
	"arien-ai-cli/internal/tools"
)

// Example: Basic usage of the Arien AI CLI components
func main() {
	// Initialize configuration
	if err := config.Initialize(); err != nil {
		log.Fatalf("Failed to initialize config: %v", err)
	}

	// Initialize authentication
	if err := auth.Initialize(); err != nil {
		log.Fatalf("Failed to initialize auth: %v", err)
	}

	// Check if any provider is authenticated
	if !auth.IsAuthenticated() {
		fmt.Println("No providers are authenticated. Please run 'arien-ai auth login' first.")
		return
	}

	// Initialize LLM manager
	llmManager := llm.NewManager()
	if err := llmManager.Initialize(); err != nil {
		log.Fatalf("Failed to initialize LLM manager: %v", err)
	}

	// Initialize tools manager
	toolsManager := tools.NewManager()
	if err := toolsManager.Initialize(); err != nil {
		log.Fatalf("Failed to initialize tools manager: %v", err)
	}

	// Example 1: Simple chat without tools
	fmt.Println("=== Example 1: Simple Chat ===")
	simpleChat(llmManager)

	// Example 2: Chat with function calling
	fmt.Println("\n=== Example 2: Chat with Function Calling ===")
	chatWithTools(llmManager, toolsManager)

	// Example 3: Direct tool usage
	fmt.Println("\n=== Example 3: Direct Tool Usage ===")
	directToolUsage(toolsManager)
}

func simpleChat(llmManager *llm.Manager) {
	ctx := context.Background()

	// Get default provider
	provider, err := llmManager.GetDefaultProvider()
	if err != nil {
		fmt.Printf("Error getting default provider: %v\n", err)
		return
	}

	// Create a simple chat request
	request := llm.ChatRequest{
		Messages: []llm.Message{
			{
				Role:    "user",
				Content: "Hello! Can you tell me what 2 + 2 equals?",
			},
		},
		Temperature: 0.7,
		MaxTokens:   100,
	}

	// Send chat request
	response, err := llmManager.Chat(ctx, provider.GetName(), request)
	if err != nil {
		fmt.Printf("Error in chat: %v\n", err)
		return
	}

	if len(response.Choices) > 0 {
		fmt.Printf("Assistant: %s\n", response.Choices[0].Message.Content)
	}
}

func chatWithTools(llmManager *llm.Manager, toolsManager *tools.Manager) {
	ctx := context.Background()

	// Get default provider
	provider, err := llmManager.GetDefaultProvider()
	if err != nil {
		fmt.Printf("Error getting default provider: %v\n", err)
		return
	}

	// Get available tools
	toolDefinitions := toolsManager.GetToolDefinitions()
	var llmTools []llm.Tool
	for _, toolDef := range toolDefinitions {
		llmTools = append(llmTools, llm.Tool{
			Type: toolDef.Type,
			Function: llm.Function{
				Name:        toolDef.Function.Name,
				Description: toolDef.Function.Description,
				Parameters:  toolDef.Function.Parameters,
			},
		})
	}

	// Create a chat request with tools
	request := llm.ChatRequest{
		Messages: []llm.Message{
			{
				Role:    "user",
				Content: "Can you calculate 15 * 23 for me and also tell me what time it is?",
			},
		},
		Tools:       llmTools,
		Temperature: 0.7,
		MaxTokens:   500,
	}

	// Send chat request
	response, err := llmManager.Chat(ctx, provider.GetName(), request)
	if err != nil {
		fmt.Printf("Error in chat: %v\n", err)
		return
	}

	if len(response.Choices) > 0 {
		choice := response.Choices[0]
		fmt.Printf("Assistant: %s\n", choice.Message.Content)

		// Handle tool calls if any
		if len(choice.Message.ToolCalls) > 0 {
			fmt.Println("\nTool calls made:")
			for _, toolCall := range choice.Message.ToolCalls {
				fmt.Printf("- %s: %s\n", toolCall.Function.Name, toolCall.Function.Arguments)

				// Execute the tool
				result, err := toolsManager.ExecuteTool(ctx, toolCall.Function.Name, toolCall.Function.Arguments)
				if err != nil {
					fmt.Printf("  Error: %v\n", err)
				} else if result.Success {
					fmt.Printf("  Result: %v\n", result.Data)
				} else {
					fmt.Printf("  Failed: %s\n", result.Error)
				}
			}
		}
	}
}

func directToolUsage(toolsManager *tools.Manager) {
	ctx := context.Background()

	// Example 1: Calculator tool
	fmt.Println("Calculator: 25 * 4")
	result, err := toolsManager.ExecuteTool(ctx, "calculate", `{
		"operation": "multiply",
		"operands": [25, 4]
	}`)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else if result.Success {
		fmt.Printf("Result: %v\n", result.Data)
	} else {
		fmt.Printf("Failed: %s\n", result.Error)
	}

	// Example 2: System info tool
	fmt.Println("\nSystem Info:")
	result, err = toolsManager.ExecuteTool(ctx, "get_system_info", `{
		"info_type": "os"
	}`)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else if result.Success {
		fmt.Printf("Result: %v\n", result.Data)
	} else {
		fmt.Printf("Failed: %s\n", result.Error)
	}

	// Example 3: Text tool
	fmt.Println("\nText manipulation:")
	result, err = toolsManager.ExecuteTool(ctx, "text_operation", `{
		"operation": "uppercase",
		"text": "hello world"
	}`)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else if result.Success {
		fmt.Printf("Result: %v\n", result.Data)
	} else {
		fmt.Printf("Failed: %s\n", result.Error)
	}

	// Example 4: Time tool
	fmt.Println("\nCurrent time:")
	result, err = toolsManager.ExecuteTool(ctx, "get_current_time", `{
		"format": "human"
	}`)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else if result.Success {
		fmt.Printf("Result: %v\n", result.Data)
	} else {
		fmt.Printf("Failed: %s\n", result.Error)
	}
}
