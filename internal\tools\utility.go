/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"strings"
	"time"
)

// CalculatorTool provides mathematical calculations
type CalculatorTool struct {
	*BaseTool
}

// NewCalculatorTool creates a new calculator tool
func NewCalculatorTool() *CalculatorTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"operation": map[string]interface{}{
				"type":        "string",
				"description": "Mathematical operation to perform",
				"enum":        []string{"add", "subtract", "multiply", "divide", "power", "sqrt", "sin", "cos", "tan", "log"},
			},
			"operands": map[string]interface{}{
				"type":        "array",
				"description": "Numbers to operate on",
				"items": map[string]interface{}{
					"type": "number",
				},
			},
		},
		"required": []string{"operation", "operands"},
	}

	return &CalculatorTool{
		BaseTool: NewBaseTool(
			"calculate",
			"Perform mathematical calculations",
			parameters,
		),
	}
}

// Execute executes the calculator tool
func (t *CalculatorTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	operation, ok := args["operation"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "operation parameter is required and must be a string",
		}, nil
	}

	operandsInterface, ok := args["operands"].([]interface{})
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "operands parameter is required and must be an array",
		}, nil
	}

	var operands []float64
	for _, op := range operandsInterface {
		if num, ok := op.(float64); ok {
			operands = append(operands, num)
		} else {
			return &ToolResult{
				Success: false,
				Error:   "all operands must be numbers",
			}, nil
		}
	}

	var result float64
	var err error

	switch operation {
	case "add":
		if len(operands) < 2 {
			return &ToolResult{Success: false, Error: "add requires at least 2 operands"}, nil
		}
		result = operands[0]
		for i := 1; i < len(operands); i++ {
			result += operands[i]
		}

	case "subtract":
		if len(operands) != 2 {
			return &ToolResult{Success: false, Error: "subtract requires exactly 2 operands"}, nil
		}
		result = operands[0] - operands[1]

	case "multiply":
		if len(operands) < 2 {
			return &ToolResult{Success: false, Error: "multiply requires at least 2 operands"}, nil
		}
		result = operands[0]
		for i := 1; i < len(operands); i++ {
			result *= operands[i]
		}

	case "divide":
		if len(operands) != 2 {
			return &ToolResult{Success: false, Error: "divide requires exactly 2 operands"}, nil
		}
		if operands[1] == 0 {
			return &ToolResult{Success: false, Error: "division by zero"}, nil
		}
		result = operands[0] / operands[1]

	case "power":
		if len(operands) != 2 {
			return &ToolResult{Success: false, Error: "power requires exactly 2 operands"}, nil
		}
		result = math.Pow(operands[0], operands[1])

	case "sqrt":
		if len(operands) != 1 {
			return &ToolResult{Success: false, Error: "sqrt requires exactly 1 operand"}, nil
		}
		if operands[0] < 0 {
			return &ToolResult{Success: false, Error: "sqrt of negative number"}, nil
		}
		result = math.Sqrt(operands[0])

	case "sin":
		if len(operands) != 1 {
			return &ToolResult{Success: false, Error: "sin requires exactly 1 operand"}, nil
		}
		result = math.Sin(operands[0])

	case "cos":
		if len(operands) != 1 {
			return &ToolResult{Success: false, Error: "cos requires exactly 1 operand"}, nil
		}
		result = math.Cos(operands[0])

	case "tan":
		if len(operands) != 1 {
			return &ToolResult{Success: false, Error: "tan requires exactly 1 operand"}, nil
		}
		result = math.Tan(operands[0])

	case "log":
		if len(operands) != 1 {
			return &ToolResult{Success: false, Error: "log requires exactly 1 operand"}, nil
		}
		if operands[0] <= 0 {
			return &ToolResult{Success: false, Error: "log of non-positive number"}, nil
		}
		result = math.Log(operands[0])

	default:
		return &ToolResult{
			Success: false,
			Error:   "unsupported operation: " + operation,
		}, nil
	}

	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"result":    result,
			"operation": operation,
			"operands":  operands,
		},
		Message: fmt.Sprintf("Calculated %s of %v = %g", operation, operands, result),
	}, nil
}

// TextTool provides text manipulation functions
type TextTool struct {
	*BaseTool
}

// NewTextTool creates a new text tool
func NewTextTool() *TextTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"operation": map[string]interface{}{
				"type":        "string",
				"description": "Text operation to perform",
				"enum":        []string{"length", "uppercase", "lowercase", "reverse", "split", "join", "replace"},
			},
			"text": map[string]interface{}{
				"type":        "string",
				"description": "Text to operate on",
			},
			"separator": map[string]interface{}{
				"type":        "string",
				"description": "Separator for split/join operations",
			},
			"old_text": map[string]interface{}{
				"type":        "string",
				"description": "Text to replace (for replace operation)",
			},
			"new_text": map[string]interface{}{
				"type":        "string",
				"description": "Replacement text (for replace operation)",
			},
			"parts": map[string]interface{}{
				"type":        "array",
				"description": "Text parts to join",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
		},
		"required": []string{"operation"},
	}

	return &TextTool{
		BaseTool: NewBaseTool(
			"text_operation",
			"Perform various text manipulation operations",
			parameters,
		),
	}
}

// Execute executes the text tool
func (t *TextTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	operation, ok := args["operation"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "operation parameter is required and must be a string",
		}, nil
	}

	var result interface{}
	var message string

	switch operation {
	case "length":
		text, ok := args["text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "text parameter is required for length operation"}, nil
		}
		result = len(text)
		message = fmt.Sprintf("Text length: %d characters", len(text))

	case "uppercase":
		text, ok := args["text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "text parameter is required for uppercase operation"}, nil
		}
		result = strings.ToUpper(text)
		message = "Converted text to uppercase"

	case "lowercase":
		text, ok := args["text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "text parameter is required for lowercase operation"}, nil
		}
		result = strings.ToLower(text)
		message = "Converted text to lowercase"

	case "reverse":
		text, ok := args["text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "text parameter is required for reverse operation"}, nil
		}
		runes := []rune(text)
		for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
			runes[i], runes[j] = runes[j], runes[i]
		}
		result = string(runes)
		message = "Reversed text"

	case "split":
		text, ok := args["text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "text parameter is required for split operation"}, nil
		}
		separator, ok := args["separator"].(string)
		if !ok {
			separator = " "
		}
		result = strings.Split(text, separator)
		message = fmt.Sprintf("Split text by '%s'", separator)

	case "join":
		partsInterface, ok := args["parts"].([]interface{})
		if !ok {
			return &ToolResult{Success: false, Error: "parts parameter is required for join operation"}, nil
		}
		var parts []string
		for _, part := range partsInterface {
			if str, ok := part.(string); ok {
				parts = append(parts, str)
			}
		}
		separator, ok := args["separator"].(string)
		if !ok {
			separator = " "
		}
		result = strings.Join(parts, separator)
		message = fmt.Sprintf("Joined %d parts with '%s'", len(parts), separator)

	case "replace":
		text, ok := args["text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "text parameter is required for replace operation"}, nil
		}
		oldText, ok := args["old_text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "old_text parameter is required for replace operation"}, nil
		}
		newText, ok := args["new_text"].(string)
		if !ok {
			return &ToolResult{Success: false, Error: "new_text parameter is required for replace operation"}, nil
		}
		result = strings.ReplaceAll(text, oldText, newText)
		message = fmt.Sprintf("Replaced '%s' with '%s'", oldText, newText)

	default:
		return &ToolResult{
			Success: false,
			Error:   "unsupported operation: " + operation,
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"result":    result,
			"operation": operation,
		},
		Message: message,
	}, nil
}

// NetworkTool provides network-related functions
type NetworkTool struct {
	*BaseTool
}

// NewNetworkTool creates a new network tool
func NewNetworkTool() *NetworkTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"operation": map[string]interface{}{
				"type":        "string",
				"description": "Network operation to perform",
				"enum":        []string{"ping", "http_get", "http_status"},
			},
			"url": map[string]interface{}{
				"type":        "string",
				"description": "URL or hostname to operate on",
			},
			"timeout": map[string]interface{}{
				"type":        "number",
				"description": "Timeout in seconds (default: 10)",
			},
		},
		"required": []string{"operation", "url"},
	}

	return &NetworkTool{
		BaseTool: NewBaseTool(
			"network_operation",
			"Perform network operations like ping and HTTP requests",
			parameters,
		),
	}
}

// Execute executes the network tool
func (t *NetworkTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	operation, ok := args["operation"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "operation parameter is required and must be a string",
		}, nil
	}

	url, ok := args["url"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "url parameter is required and must be a string",
		}, nil
	}

	timeout := 10 * time.Second
	if timeoutInterface, exists := args["timeout"]; exists {
		if timeoutFloat, ok := timeoutInterface.(float64); ok {
			timeout = time.Duration(timeoutFloat) * time.Second
		}
	}

	switch operation {
	case "http_get", "http_status":
		client := &http.Client{Timeout: timeout}
		
		start := time.Now()
		resp, err := client.Get(url)
		duration := time.Since(start)
		
		if err != nil {
			return &ToolResult{
				Success: false,
				Error:   fmt.Sprintf("HTTP request failed: %v", err),
			}, nil
		}
		defer resp.Body.Close()

		result := map[string]interface{}{
			"status_code":    resp.StatusCode,
			"status":         resp.Status,
			"response_time":  duration.Milliseconds(),
			"content_length": resp.ContentLength,
			"headers":        resp.Header,
		}

		if operation == "http_get" && resp.ContentLength < 10000 { // Limit response size
			// Read a limited amount of content
			buffer := make([]byte, 1000)
			n, _ := resp.Body.Read(buffer)
			result["content_preview"] = string(buffer[:n])
		}

		return &ToolResult{
			Success: true,
			Data:    result,
			Message: fmt.Sprintf("HTTP %s to %s completed in %dms", operation, url, duration.Milliseconds()),
		}, nil

	case "ping":
		// Simple HTTP-based ping since ICMP requires privileges
		client := &http.Client{Timeout: timeout}
		
		start := time.Now()
		resp, err := client.Head(url)
		duration := time.Since(start)
		
		success := err == nil
		var statusCode int
		if resp != nil {
			statusCode = resp.StatusCode
			resp.Body.Close()
		}

		return &ToolResult{
			Success: true,
			Data: map[string]interface{}{
				"reachable":     success,
				"response_time": duration.Milliseconds(),
				"status_code":   statusCode,
				"error":         func() string { if err != nil { return err.Error() }; return "" }(),
			},
			Message: fmt.Sprintf("Ping to %s: %s (%dms)", url, map[bool]string{true: "success", false: "failed"}[success], duration.Milliseconds()),
		}, nil

	default:
		return &ToolResult{
			Success: false,
			Error:   "unsupported operation: " + operation,
		}, nil
	}
}
