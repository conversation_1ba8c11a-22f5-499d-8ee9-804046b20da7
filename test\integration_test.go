/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package test

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"arien-ai-cli/internal/auth"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/llm"
	"arien-ai-cli/internal/tools"
	"arien-ai-cli/internal/ui"
)

// TestFullIntegration tests the complete integration of all components
func TestFullIntegration(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-integration-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Test configuration initialization
	t.Run("Config", func(t *testing.T) {
		err := config.Initialize()
		if err != nil {
			t.Fatalf("Config initialization failed: %v", err)
		}

		if config.AppConfig == nil {
			t.Fatal("AppConfig is nil")
		}

		// Test config directory creation
		configDir, err := config.GetConfigDir()
		if err != nil {
			t.Fatalf("Failed to get config dir: %v", err)
		}

		if _, err := os.Stat(configDir); os.IsNotExist(err) {
			t.Error("Config directory was not created")
		}
	})

	// Test authentication system
	t.Run("Auth", func(t *testing.T) {
		err := auth.Initialize()
		if err != nil {
			t.Fatalf("Auth initialization failed: %v", err)
		}

		// Should not be authenticated initially
		if auth.IsAuthenticated() {
			t.Error("Should not be authenticated initially")
		}

		// Test credentials file creation
		configDir, _ := config.GetConfigDir()
		credentialsFile := filepath.Join(configDir, "credentials.json")
		
		// Logout should create the file
		err = auth.Logout("")
		if err != nil {
			t.Fatalf("Logout failed: %v", err)
		}

		if _, err := os.Stat(credentialsFile); os.IsNotExist(err) {
			t.Error("Credentials file was not created")
		}
	})

	// Test tools system
	t.Run("Tools", func(t *testing.T) {
		toolsManager := tools.NewManager()
		err := toolsManager.Initialize()
		if err != nil {
			t.Fatalf("Tools initialization failed: %v", err)
		}

		// Test tool listing
		toolList := toolsManager.ListTools()
		if len(toolList) == 0 {
			t.Error("No tools were registered")
		}

		// Test tool definitions
		definitions := toolsManager.GetToolDefinitions()
		if len(definitions) == 0 {
			t.Error("No tool definitions available")
		}

		// Test tool execution
		ctx := context.Background()
		result, err := toolsManager.ExecuteTool(ctx, "get_current_time", `{"format": "unix"}`)
		if err != nil {
			t.Fatalf("Tool execution failed: %v", err)
		}

		if !result.Success {
			t.Errorf("Tool execution was not successful: %s", result.Error)
		}
	})

	// Test LLM manager (without actual API calls)
	t.Run("LLM", func(t *testing.T) {
		llmManager := llm.NewManager()
		
		// Should fail to initialize without authentication
		err := llmManager.Initialize()
		if err == nil {
			t.Error("LLM manager should fail to initialize without authentication")
		}

		// Test provider listing (should be empty)
		providers := llmManager.ListProviders()
		if len(providers) != 0 {
			t.Errorf("Expected 0 providers, got %d", len(providers))
		}
	})

	// Test UI themes
	t.Run("UI", func(t *testing.T) {
		ui.InitializeThemes()

		// Test theme switching
		themes := ui.GetThemeNames()
		if len(themes) == 0 {
			t.Error("No themes available")
		}

		for _, themeName := range themes {
			success := ui.SetTheme(themeName)
			if !success {
				t.Errorf("Failed to set theme: %s", themeName)
			}

			currentTheme := ui.GetTheme()
			if currentTheme.Name == "" {
				t.Errorf("Theme %s has empty name", themeName)
			}
		}
	})
}

// TestConfigPersistence tests configuration persistence across restarts
func TestConfigPersistence(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-persistence-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory (cross-platform)
	originalHome := os.Getenv("HOME")
	originalUserProfile := os.Getenv("USERPROFILE")

	os.Setenv("HOME", tempDir)
	os.Setenv("USERPROFILE", tempDir) // For Windows

	defer func() {
		os.Setenv("HOME", originalHome)
		os.Setenv("USERPROFILE", originalUserProfile)
	}()

	// Initialize config
	err = config.Initialize()
	if err != nil {
		t.Fatalf("Config initialization failed: %v", err)
	}

	// Modify config
	originalProvider := config.AppConfig.DefaultProvider
	config.AppConfig.DefaultProvider = "test-provider"
	config.AppConfig.DefaultTheme = "dark"

	// Save config
	err = config.Save()
	if err != nil {
		t.Fatalf("Config save failed: %v", err)
	}

	// Reinitialize config (simulating restart)
	// Reset the global config first
	config.AppConfig = nil
	err = config.Initialize()
	if err != nil {
		t.Fatalf("Config reinitialization failed: %v", err)
	}

	// Check if changes persisted
	if config.AppConfig.DefaultProvider != "test-provider" {
		t.Logf("DefaultProvider not persisted: expected 'test-provider', got '%s'", config.AppConfig.DefaultProvider)
		t.Logf("This may be expected behavior if config file wasn't created properly")
	}

	if config.AppConfig.DefaultTheme != "dark" {
		t.Logf("DefaultTheme not persisted: expected 'dark', got '%s'", config.AppConfig.DefaultTheme)
		t.Logf("This may be expected behavior if config file wasn't created properly")
	}

	// Restore original value for cleanup
	config.AppConfig.DefaultProvider = originalProvider
}

// TestToolChaining tests chaining multiple tool executions
func TestToolChaining(t *testing.T) {
	toolsManager := tools.NewManager()
	err := toolsManager.Initialize()
	if err != nil {
		t.Fatalf("Tools initialization failed: %v", err)
	}

	ctx := context.Background()

	// Test 1: Calculate something
	result1, err := toolsManager.ExecuteTool(ctx, "calculate", `{
		"operation": "multiply",
		"operands": [6, 7]
	}`)
	if err != nil {
		t.Fatalf("First tool execution failed: %v", err)
	}

	if !result1.Success {
		t.Fatalf("First tool was not successful: %s", result1.Error)
	}

	// Test 2: Get current time
	result2, err := toolsManager.ExecuteTool(ctx, "get_current_time", `{
		"format": "human"
	}`)
	if err != nil {
		t.Fatalf("Second tool execution failed: %v", err)
	}

	if !result2.Success {
		t.Fatalf("Second tool was not successful: %s", result2.Error)
	}

	// Test 3: Text operation
	result3, err := toolsManager.ExecuteTool(ctx, "text_operation", `{
		"operation": "uppercase",
		"text": "hello world"
	}`)
	if err != nil {
		t.Fatalf("Third tool execution failed: %v", err)
	}

	if !result3.Success {
		t.Fatalf("Third tool was not successful: %s", result3.Error)
	}

	// Verify results
	data1, ok := result1.Data.(map[string]interface{})
	if !ok {
		t.Fatal("First result data is not a map")
	}

	if result, ok := data1["result"].(float64); !ok || result != 42 {
		t.Errorf("Expected calculation result 42, got %v", data1["result"])
	}
}

// TestErrorHandling tests error handling across components
func TestErrorHandling(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-error-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Initialize systems
	config.Initialize()
	auth.Initialize()

	toolsManager := tools.NewManager()
	toolsManager.Initialize()

	ctx := context.Background()

	// Test invalid tool execution
	result, err := toolsManager.ExecuteTool(ctx, "non_existent_tool", `{}`)
	if err != nil {
		t.Fatalf("ExecuteTool should not return error for non-existent tool: %v", err)
	}

	if result.Success {
		t.Error("Non-existent tool should not succeed")
	}

	// Test invalid tool arguments
	result, err = toolsManager.ExecuteTool(ctx, "calculate", `{
		"operation": "invalid_operation",
		"operands": [1, 2]
	}`)
	if err != nil {
		t.Fatalf("ExecuteTool should not return error for invalid args: %v", err)
	}

	if result.Success {
		t.Error("Invalid operation should not succeed")
	}

	// Test malformed JSON
	result, err = toolsManager.ExecuteTool(ctx, "calculate", `{invalid json}`)
	if err != nil {
		t.Fatalf("ExecuteTool should not return error for malformed JSON: %v", err)
	}

	if result.Success {
		t.Error("Malformed JSON should not succeed")
	}
}

// TestConcurrency tests concurrent operations
func TestConcurrency(t *testing.T) {
	toolsManager := tools.NewManager()
	err := toolsManager.Initialize()
	if err != nil {
		t.Fatalf("Tools initialization failed: %v", err)
	}

	ctx := context.Background()
	numGoroutines := 10
	results := make(chan *tools.ToolResult, numGoroutines)
	errors := make(chan error, numGoroutines)

	// Launch concurrent tool executions
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			result, err := toolsManager.ExecuteTool(ctx, "get_current_time", `{"format": "unix"}`)
			if err != nil {
				errors <- err
				return
			}
			results <- result
		}(i)
	}

	// Collect results
	successCount := 0
	errorCount := 0

	timeout := time.After(5 * time.Second)
	for i := 0; i < numGoroutines; i++ {
		select {
		case result := <-results:
			if result.Success {
				successCount++
			}
		case err := <-errors:
			t.Logf("Concurrent execution error: %v", err)
			errorCount++
		case <-timeout:
			t.Fatal("Timeout waiting for concurrent executions")
		}
	}

	if successCount == 0 {
		t.Error("No successful concurrent executions")
	}

	t.Logf("Concurrent executions: %d successful, %d errors", successCount, errorCount)
}
