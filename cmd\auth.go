/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
	"arien-ai-cli/internal/auth"
)

// authCmd represents the auth command
var authCmd = &cobra.Command{
	Use:   "auth",
	Short: "Authentication management for LLM providers",
	Long:  `Manage authentication for various LLM providers including DeepSeek, OpenAI, Google Gemini, and Anthropic.`,
}

var loginCmd = &cobra.Command{
	Use:   "login",
	Short: "Login to LLM providers",
	Long:  `Interactive login process for LLM providers with API key and OAuth support.`,
	Run: func(cmd *cobra.Command, args []string) {
		if err := auth.ShowAuthScreen(); err != nil {
			fmt.Printf("Authentication failed: %v\n", err)
			return
		}
		fmt.Println("Authentication successful!")
	},
}

var logoutCmd = &cobra.Command{
	Use:   "logout",
	Short: "Logout from LLM providers",
	Long:  `Clear stored authentication credentials for all or specific LLM providers.`,
	Run: func(cmd *cobra.Command, args []string) {
		provider, _ := cmd.Flags().GetString("provider")
		if err := auth.Logout(provider); err != nil {
			fmt.Printf("Logout failed: %v\n", err)
			return
		}
		if provider == "" {
			fmt.Println("Logged out from all providers")
		} else {
			fmt.Printf("Logged out from %s\n", provider)
		}
	},
}

var statusCmd = &cobra.Command{
	Use:   "status",
	Short: "Show authentication status",
	Long:  `Display current authentication status for all LLM providers.`,
	Run: func(cmd *cobra.Command, args []string) {
		auth.ShowStatus()
	},
}

func init() {
	rootCmd.AddCommand(authCmd)
	authCmd.AddCommand(loginCmd)
	authCmd.AddCommand(logoutCmd)
	authCmd.AddCommand(statusCmd)

	// Flags for logout command
	logoutCmd.Flags().StringP("provider", "p", "", "specific provider to logout from (deepseek, openai, gemini, anthropic)")
}
