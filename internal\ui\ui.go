/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"fmt"

	"github.com/charmbracelet/bubbletea"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/logger"
)

// StartInteractiveMode starts the main interactive chat interface
func StartInteractiveMode() error {
	logger.Info("Starting interactive mode")

	// Initialize themes
	InitializeThemes()
	
	// Set theme from config
	if config.AppConfig.DefaultTheme != "" {
		SetTheme(config.AppConfig.DefaultTheme)
	}

	// Determine provider and model
	provider := config.AppConfig.DefaultProvider
	if provider == "" {
		provider = "openai" // fallback
	}

	model := config.AppConfig.DefaultModel
	if model == "" {
		// Get default model for provider
		if providerConfig, exists := config.AppConfig.Providers[provider]; exists {
			model = providerConfig.DefaultModel
		}
		if model == "" {
			model = "gpt-4" // fallback
		}
	}

	// Create and run chat model
	chatModel := NewChatModel(provider, model)
	program := tea.NewProgram(chatModel, tea.WithAltScreen())
	
	_, err := program.Run()
	if err != nil {
		return fmt.Errorf("failed to run interactive mode: %w", err)
	}

	return nil
}

// StartChat starts a chat session with specific parameters
func StartChat(provider, model, theme string) error {
	logger.Info(fmt.Sprintf("Starting chat with provider: %s, model: %s, theme: %s", provider, model, theme))

	// Initialize themes
	InitializeThemes()
	
	// Set theme
	if theme != "" {
		if !SetTheme(theme) {
			logger.Warning(fmt.Sprintf("Theme '%s' not found, using default", theme))
		}
	}

	// Use defaults if not specified
	if provider == "" {
		provider = config.AppConfig.DefaultProvider
		if provider == "" {
			provider = "openai"
		}
	}

	if model == "" {
		if providerConfig, exists := config.AppConfig.Providers[provider]; exists {
			model = providerConfig.DefaultModel
		}
		if model == "" {
			model = "gpt-4"
		}
	}

	// Create and run chat model
	chatModel := NewChatModel(provider, model)
	program := tea.NewProgram(chatModel, tea.WithAltScreen())
	
	_, err := program.Run()
	if err != nil {
		return fmt.Errorf("failed to start chat: %w", err)
	}

	return nil
}

// ShowThemeSelector shows the theme selection interface
func ShowThemeSelector() error {
	logger.Info("Starting theme selector")

	// Initialize themes
	InitializeThemes()

	// Create and run theme selector
	themeSelectorModel := NewThemeSelectorModel()
	program := tea.NewProgram(themeSelectorModel, tea.WithAltScreen())
	
	_, err := program.Run()
	if err != nil {
		return fmt.Errorf("failed to show theme selector: %w", err)
	}

	return nil
}
