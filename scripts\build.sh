#!/bin/bash

# Arien AI CLI Build Script
# Copyright 2025 Arien LLC
# License: MIT

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BINARY_NAME="arien-ai"
VERSION=${VERSION:-"1.0.0"}
BUILD_DIR="build"
DIST_DIR="dist"

# Build information
BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")

# LDFLAGS for build information
LDFLAGS="-ldflags \"-X main.version=${VERSION} -X main.buildTime=${BUILD_TIME} -X main.gitCommit=${GIT_COMMIT} -X main.gitBranch=${GIT_BRANCH}\""

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v go &> /dev/null; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go version: ${GO_VERSION}"
    
    if ! command -v git &> /dev/null; then
        log_warning "Git is not installed - build information will be limited"
    fi
}

clean() {
    log_info "Cleaning build artifacts..."
    rm -rf "${BUILD_DIR}"
    rm -rf "${DIST_DIR}"
    rm -f coverage.out coverage.html
    log_success "Clean completed"
}

setup_dirs() {
    log_info "Setting up build directories..."
    mkdir -p "${BUILD_DIR}"
    mkdir -p "${DIST_DIR}"
}

run_tests() {
    log_info "Running tests..."
    go test -v ./...
    if [ $? -eq 0 ]; then
        log_success "All tests passed"
    else
        log_error "Tests failed"
        exit 1
    fi
}

run_linter() {
    log_info "Running linter..."
    if command -v golangci-lint &> /dev/null; then
        golangci-lint run
        if [ $? -eq 0 ]; then
            log_success "Linting passed"
        else
            log_error "Linting failed"
            exit 1
        fi
    else
        log_warning "golangci-lint not found, skipping linting"
    fi
}

build_single() {
    local os=$1
    local arch=$2
    local output_name="${BINARY_NAME}"
    
    if [ "$os" = "windows" ]; then
        output_name="${output_name}.exe"
    fi
    
    local output_path="${BUILD_DIR}/${output_name}"
    if [ "$os" != "$(go env GOOS)" ] || [ "$arch" != "$(go env GOARCH)" ]; then
        output_path="${BUILD_DIR}/${BINARY_NAME}-${os}-${arch}"
        if [ "$os" = "windows" ]; then
            output_path="${output_path}.exe"
        fi
    fi
    
    log_info "Building for ${os}/${arch}..."
    
    GOOS=$os GOARCH=$arch eval "go build ${LDFLAGS} -o ${output_path} ."
    
    if [ $? -eq 0 ]; then
        log_success "Built ${output_path}"
        
        # Create distribution package
        local dist_name="${BINARY_NAME}-${VERSION}-${os}-${arch}"
        local dist_path="${DIST_DIR}/${dist_name}"
        
        mkdir -p "${dist_path}"
        cp "${output_path}" "${dist_path}/"
        cp README.md "${dist_path}/"
        cp LICENSE "${dist_path}/"
        cp .env.example "${dist_path}/"
        
        # Create archive
        cd "${DIST_DIR}"
        if [ "$os" = "windows" ]; then
            zip -r "${dist_name}.zip" "${dist_name}/"
        else
            tar -czf "${dist_name}.tar.gz" "${dist_name}/"
        fi
        cd ..
        
        rm -rf "${dist_path}"
        log_success "Created distribution package for ${os}/${arch}"
    else
        log_error "Build failed for ${os}/${arch}"
        exit 1
    fi
}

build_all() {
    log_info "Building for all platforms..."
    
    # Define target platforms
    platforms=(
        "linux/amd64"
        "linux/arm64"
        "darwin/amd64"
        "darwin/arm64"
        "windows/amd64"
        "windows/arm64"
    )
    
    for platform in "${platforms[@]}"; do
        IFS='/' read -r os arch <<< "$platform"
        build_single "$os" "$arch"
    done
    
    log_success "All platform builds completed"
}

build_current() {
    log_info "Building for current platform..."
    local current_os=$(go env GOOS)
    local current_arch=$(go env GOARCH)
    build_single "$current_os" "$current_arch"
}

generate_checksums() {
    log_info "Generating checksums..."
    cd "${DIST_DIR}"
    
    if command -v sha256sum &> /dev/null; then
        sha256sum *.tar.gz *.zip > checksums.txt 2>/dev/null || true
    elif command -v shasum &> /dev/null; then
        shasum -a 256 *.tar.gz *.zip > checksums.txt 2>/dev/null || true
    else
        log_warning "No checksum utility found"
    fi
    
    cd ..
    log_success "Checksums generated"
}

show_help() {
    echo "Arien AI CLI Build Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  build       Build for current platform (default)"
    echo "  build-all   Build for all supported platforms"
    echo "  test        Run tests"
    echo "  lint        Run linter"
    echo "  clean       Clean build artifacts"
    echo "  package     Build all platforms and create distribution packages"
    echo "  help        Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  VERSION     Version to build (default: 1.0.0)"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  VERSION=1.2.3 $0 build-all"
    echo "  $0 test && $0 build"
}

main() {
    local command=${1:-"build"}
    
    case $command in
        "build")
            check_dependencies
            setup_dirs
            build_current
            ;;
        "build-all")
            check_dependencies
            setup_dirs
            build_all
            ;;
        "test")
            check_dependencies
            run_tests
            ;;
        "lint")
            check_dependencies
            run_linter
            ;;
        "clean")
            clean
            ;;
        "package")
            check_dependencies
            clean
            setup_dirs
            run_tests
            run_linter
            build_all
            generate_checksums
            log_success "Package build completed"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
