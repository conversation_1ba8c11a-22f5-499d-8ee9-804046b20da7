/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package main

import (
	"fmt"
	"os"

	"arien-ai-cli/cmd"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/logger"
)

func main() {
	// Initialize configuration
	if err := config.Initialize(); err != nil {
		fmt.Fprintf(os.Stderr, "Error initializing configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	logger.Initialize()

	// Execute root command
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
