/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package tools

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"time"
)

// SystemInfoTool provides system information
type SystemInfoTool struct {
	*BaseTool
}

// NewSystemInfoTool creates a new system info tool
func NewSystemInfoTool() *SystemInfoTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"info_type": map[string]interface{}{
				"type":        "string",
				"description": "Type of system information to retrieve",
				"enum":        []string{"os", "cpu", "memory", "disk", "network", "all"},
			},
		},
		"required": []string{"info_type"},
	}

	return &SystemInfoTool{
		BaseTool: NewBaseTool(
			"get_system_info",
			"Get system information including OS, CPU, memory, disk, and network details",
			parameters,
		),
	}
}

// Execute executes the system info tool
func (t *SystemInfoTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	infoType, ok := args["info_type"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "info_type parameter is required and must be a string",
		}, nil
	}

	var result map[string]interface{}

	switch infoType {
	case "os":
		result = t.getOSInfo()
	case "cpu":
		result = t.getCPUInfo()
	case "memory":
		result = t.getMemoryInfo()
	case "disk":
		result = t.getDiskInfo()
	case "network":
		result = t.getNetworkInfo()
	case "all":
		result = map[string]interface{}{
			"os":      t.getOSInfo(),
			"cpu":     t.getCPUInfo(),
			"memory":  t.getMemoryInfo(),
			"disk":    t.getDiskInfo(),
			"network": t.getNetworkInfo(),
		}
	default:
		return &ToolResult{
			Success: false,
			Error:   "Invalid info_type. Must be one of: os, cpu, memory, disk, network, all",
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data:    result,
		Message: fmt.Sprintf("Retrieved %s information", infoType),
	}, nil
}

func (t *SystemInfoTool) getOSInfo() map[string]interface{} {
	hostname, _ := os.Hostname()
	return map[string]interface{}{
		"os":           runtime.GOOS,
		"architecture": runtime.GOARCH,
		"hostname":     hostname,
		"go_version":   runtime.Version(),
		"num_cpu":      runtime.NumCPU(),
	}
}

func (t *SystemInfoTool) getCPUInfo() map[string]interface{} {
	return map[string]interface{}{
		"num_cpu":      runtime.NumCPU(),
		"architecture": runtime.GOARCH,
		"go_version":   runtime.Version(),
	}
}

func (t *SystemInfoTool) getMemoryInfo() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"alloc_mb":      m.Alloc / 1024 / 1024,
		"total_alloc_mb": m.TotalAlloc / 1024 / 1024,
		"sys_mb":        m.Sys / 1024 / 1024,
		"num_gc":        m.NumGC,
	}
}

func (t *SystemInfoTool) getDiskInfo() map[string]interface{} {
	// Basic disk info - could be expanded with more detailed information
	wd, _ := os.Getwd()
	return map[string]interface{}{
		"working_directory": wd,
		"temp_directory":    os.TempDir(),
	}
}

func (t *SystemInfoTool) getNetworkInfo() map[string]interface{} {
	// Basic network info - could be expanded
	return map[string]interface{}{
		"hostname": func() string {
			hostname, _ := os.Hostname()
			return hostname
		}(),
	}
}

// CommandTool executes system commands
type CommandTool struct {
	*BaseTool
}

// NewCommandTool creates a new command execution tool
func NewCommandTool() *CommandTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"command": map[string]interface{}{
				"type":        "string",
				"description": "Command to execute",
			},
			"args": map[string]interface{}{
				"type":        "array",
				"description": "Command arguments",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
			"timeout": map[string]interface{}{
				"type":        "number",
				"description": "Timeout in seconds (default: 30)",
			},
		},
		"required": []string{"command"},
	}

	return &CommandTool{
		BaseTool: NewBaseTool(
			"execute_command",
			"Execute a system command with optional arguments and timeout",
			parameters,
		),
	}
}

// Execute executes the command tool
func (t *CommandTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	command, ok := args["command"].(string)
	if !ok {
		return &ToolResult{
			Success: false,
			Error:   "command parameter is required and must be a string",
		}, nil
	}

	// Get arguments
	var cmdArgs []string
	if argsInterface, exists := args["args"]; exists {
		if argsList, ok := argsInterface.([]interface{}); ok {
			for _, arg := range argsList {
				if argStr, ok := arg.(string); ok {
					cmdArgs = append(cmdArgs, argStr)
				}
			}
		}
	}

	// Get timeout
	timeout := 30 * time.Second
	if timeoutInterface, exists := args["timeout"]; exists {
		if timeoutFloat, ok := timeoutInterface.(float64); ok {
			timeout = time.Duration(timeoutFloat) * time.Second
		}
	}

	// Create context with timeout
	cmdCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// Execute command
	cmd := exec.CommandContext(cmdCtx, command, cmdArgs...)
	output, err := cmd.CombinedOutput()

	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   err.Error(),
			Data: map[string]interface{}{
				"output":     string(output),
				"exit_code":  cmd.ProcessState.ExitCode(),
			},
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"output":    string(output),
			"exit_code": 0,
		},
		Message: fmt.Sprintf("Command '%s' executed successfully", command),
	}, nil
}

// TimeTool provides time-related functions
type TimeTool struct {
	*BaseTool
}

// NewTimeTool creates a new time tool
func NewTimeTool() *TimeTool {
	parameters := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"format": map[string]interface{}{
				"type":        "string",
				"description": "Time format (default: RFC3339)",
				"enum":        []string{"RFC3339", "unix", "human", "iso"},
			},
			"timezone": map[string]interface{}{
				"type":        "string",
				"description": "Timezone (default: local)",
			},
		},
	}

	return &TimeTool{
		BaseTool: NewBaseTool(
			"get_current_time",
			"Get the current date and time in various formats",
			parameters,
		),
	}
}

// Execute executes the time tool
func (t *TimeTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
	format := "RFC3339"
	if formatInterface, exists := args["format"]; exists {
		if formatStr, ok := formatInterface.(string); ok {
			format = formatStr
		}
	}

	timezone := "local"
	if timezoneInterface, exists := args["timezone"]; exists {
		if timezoneStr, ok := timezoneInterface.(string); ok {
			timezone = timezoneStr
		}
	}

	now := time.Now()
	
	// Handle timezone
	if timezone != "local" {
		if loc, err := time.LoadLocation(timezone); err == nil {
			now = now.In(loc)
		}
	}

	var timeStr string
	switch format {
	case "RFC3339":
		timeStr = now.Format(time.RFC3339)
	case "unix":
		timeStr = fmt.Sprintf("%d", now.Unix())
	case "human":
		timeStr = now.Format("Monday, January 2, 2006 at 3:04 PM MST")
	case "iso":
		timeStr = now.Format("2006-01-02T15:04:05Z07:00")
	default:
		timeStr = now.Format(time.RFC3339)
	}

	return &ToolResult{
		Success: true,
		Data: map[string]interface{}{
			"time":      timeStr,
			"timestamp": now.Unix(),
			"timezone":  now.Location().String(),
			"format":    format,
		},
		Message: fmt.Sprintf("Current time in %s format", format),
	}, nil
}
