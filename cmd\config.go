/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
	"arien-ai-cli/internal/config"
)

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Configuration management",
	Long:  `Manage configuration settings for Arien AI CLI.`,
}

var configShowCmd = &cobra.Command{
	Use:   "show",
	Short: "Show current configuration",
	Long:  `Display the current configuration settings.`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("Configuration:\n")
		fmt.Printf("  Debug: %v\n", config.AppConfig.Debug)
		fmt.Printf("  Default Provider: %s\n", config.AppConfig.DefaultProvider)
		fmt.Printf("  Default Model: %s\n", config.AppConfig.DefaultModel)
		fmt.Printf("  Default Theme: %s\n", config.AppConfig.DefaultTheme)
		fmt.Printf("  System Prompt: %s\n", config.AppConfig.SystemPrompt)
		
		fmt.Printf("\nProviders:\n")
		for name, provider := range config.AppConfig.Providers {
			fmt.Printf("  %s:\n", name)
			fmt.Printf("    Base URL: %s\n", provider.BaseURL)
			fmt.Printf("    Default Model: %s\n", provider.DefaultModel)
			fmt.Printf("    Models: %v\n", provider.Models)
		}
	},
}

var configSetCmd = &cobra.Command{
	Use:   "set [key] [value]",
	Short: "Set a configuration value",
	Long:  `Set a configuration value. Available keys: default_provider, default_model, default_theme, system_prompt`,
	Args:  cobra.ExactArgs(2),
	Run: func(cmd *cobra.Command, args []string) {
		key := args[0]
		value := args[1]

		switch key {
		case "default_provider":
			config.AppConfig.DefaultProvider = value
		case "default_model":
			config.AppConfig.DefaultModel = value
		case "default_theme":
			config.AppConfig.DefaultTheme = value
		case "system_prompt":
			config.AppConfig.SystemPrompt = value
		case "debug":
			config.AppConfig.Debug = value == "true"
		default:
			fmt.Printf("Unknown configuration key: %s\n", key)
			return
		}

		if err := config.Save(); err != nil {
			fmt.Printf("Failed to save configuration: %v\n", err)
			return
		}

		fmt.Printf("Configuration updated: %s = %s\n", key, value)
	},
}

var configResetCmd = &cobra.Command{
	Use:   "reset",
	Short: "Reset configuration to defaults",
	Long:  `Reset all configuration settings to their default values.`,
	Run: func(cmd *cobra.Command, args []string) {
		if err := config.Initialize(); err != nil {
			fmt.Printf("Failed to reset configuration: %v\n", err)
			return
		}
		fmt.Println("Configuration reset to defaults")
	},
}

func init() {
	rootCmd.AddCommand(configCmd)
	configCmd.AddCommand(configShowCmd)
	configCmd.AddCommand(configSetCmd)
	configCmd.AddCommand(configResetCmd)
}
