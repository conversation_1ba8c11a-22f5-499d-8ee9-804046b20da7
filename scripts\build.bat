@echo off
REM Arien AI CLI Build Script for Windows
REM Copyright 2025 Arien LLC
REM License: MIT

setlocal enabledelayedexpansion

REM Configuration
set BINARY_NAME=arien-ai
set VERSION=%VERSION%
if "%VERSION%"=="" set VERSION=1.0.0
set BUILD_DIR=build
set DIST_DIR=dist

REM Build information
for /f "tokens=*" %%i in ('powershell -command "Get-Date -Format 'yyyy-MM-ddTHH:mm:ssZ'"') do set BUILD_TIME=%%i
for /f "tokens=*" %%i in ('git rev-parse --short HEAD 2^>nul') do set GIT_COMMIT=%%i
if "%GIT_COMMIT%"=="" set GIT_COMMIT=unknown
for /f "tokens=*" %%i in ('git rev-parse --abbrev-ref HEAD 2^>nul') do set GIT_BRANCH=%%i
if "%GIT_BRANCH%"=="" set GIT_BRANCH=unknown

REM LDFLAGS for build information
set LDFLAGS=-ldflags "-X main.version=%VERSION% -X main.buildTime=%BUILD_TIME% -X main.gitCommit=%GIT_COMMIT% -X main.gitBranch=%GIT_BRANCH%"

REM Functions
goto :main

:log_info
echo [INFO] %~1
goto :eof

:log_success
echo [SUCCESS] %~1
goto :eof

:log_warning
echo [WARNING] %~1
goto :eof

:log_error
echo [ERROR] %~1
goto :eof

:check_dependencies
call :log_info "Checking dependencies..."

where go >nul 2>&1
if errorlevel 1 (
    call :log_error "Go is not installed or not in PATH"
    exit /b 1
)

for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
set GO_VERSION=%GO_VERSION:go=%
call :log_info "Go version: %GO_VERSION%"

where git >nul 2>&1
if errorlevel 1 (
    call :log_warning "Git is not installed - build information will be limited"
)
goto :eof

:clean
call :log_info "Cleaning build artifacts..."
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
if exist "coverage.out" del "coverage.out"
if exist "coverage.html" del "coverage.html"
call :log_success "Clean completed"
goto :eof

:setup_dirs
call :log_info "Setting up build directories..."
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%DIST_DIR%" mkdir "%DIST_DIR%"
goto :eof

:run_tests
call :log_info "Running tests..."
go test -v ./...
if errorlevel 1 (
    call :log_error "Tests failed"
    exit /b 1
)
call :log_success "All tests passed"
goto :eof

:run_linter
call :log_info "Running linter..."
where golangci-lint >nul 2>&1
if errorlevel 1 (
    call :log_warning "golangci-lint not found, skipping linting"
    goto :eof
)

golangci-lint run
if errorlevel 1 (
    call :log_error "Linting failed"
    exit /b 1
)
call :log_success "Linting passed"
goto :eof

:build_single
set target_os=%~1
set target_arch=%~2
set output_name=%BINARY_NAME%

if "%target_os%"=="windows" set output_name=%output_name%.exe

set output_path=%BUILD_DIR%\%output_name%
for /f %%i in ('go env GOOS') do set current_os=%%i
for /f %%i in ('go env GOARCH') do set current_arch=%%i

if not "%target_os%"=="%current_os%" (
    set output_path=%BUILD_DIR%\%BINARY_NAME%-%target_os%-%target_arch%
    if "%target_os%"=="windows" set output_path=!output_path!.exe
) else if not "%target_arch%"=="%current_arch%" (
    set output_path=%BUILD_DIR%\%BINARY_NAME%-%target_os%-%target_arch%
    if "%target_os%"=="windows" set output_path=!output_path!.exe
)

call :log_info "Building for %target_os%/%target_arch%..."

set GOOS=%target_os%
set GOARCH=%target_arch%
go build %LDFLAGS% -o "%output_path%" .

if errorlevel 1 (
    call :log_error "Build failed for %target_os%/%target_arch%"
    exit /b 1
)

call :log_success "Built %output_path%"

REM Create distribution package
set dist_name=%BINARY_NAME%-%VERSION%-%target_os%-%target_arch%
set dist_path=%DIST_DIR%\%dist_name%

if not exist "%dist_path%" mkdir "%dist_path%"
copy "%output_path%" "%dist_path%\"
copy "README.md" "%dist_path%\"
copy "LICENSE" "%dist_path%\"
copy ".env.example" "%dist_path%\"

REM Create archive
cd "%DIST_DIR%"
if "%target_os%"=="windows" (
    powershell -command "Compress-Archive -Path '%dist_name%' -DestinationPath '%dist_name%.zip'"
) else (
    powershell -command "Compress-Archive -Path '%dist_name%' -DestinationPath '%dist_name%.zip'"
)
cd ..

rmdir /s /q "%dist_path%"
call :log_success "Created distribution package for %target_os%/%target_arch%"
goto :eof

:build_all
call :log_info "Building for all platforms..."

call :build_single "linux" "amd64"
call :build_single "linux" "arm64"
call :build_single "darwin" "amd64"
call :build_single "darwin" "arm64"
call :build_single "windows" "amd64"
call :build_single "windows" "arm64"

call :log_success "All platform builds completed"
goto :eof

:build_current
call :log_info "Building for current platform..."
for /f %%i in ('go env GOOS') do set current_os=%%i
for /f %%i in ('go env GOARCH') do set current_arch=%%i
call :build_single "%current_os%" "%current_arch%"
goto :eof

:generate_checksums
call :log_info "Generating checksums..."
cd "%DIST_DIR%"
powershell -command "Get-ChildItem *.zip | ForEach-Object { $hash = Get-FileHash $_.Name -Algorithm SHA256; $hash.Algorithm + ' ' + $hash.Hash.ToLower() + ' ' + $hash.Path } | Out-File -Encoding ASCII checksums.txt"
cd ..
call :log_success "Checksums generated"
goto :eof

:show_help
echo Arien AI CLI Build Script
echo.
echo Usage: %~nx0 [command]
echo.
echo Commands:
echo   build       Build for current platform (default)
echo   build-all   Build for all supported platforms
echo   test        Run tests
echo   lint        Run linter
echo   clean       Clean build artifacts
echo   package     Build all platforms and create distribution packages
echo   help        Show this help message
echo.
echo Environment variables:
echo   VERSION     Version to build (default: 1.0.0)
echo.
echo Examples:
echo   %~nx0 build
echo   set VERSION=1.2.3 && %~nx0 build-all
echo   %~nx0 test && %~nx0 build
goto :eof

:main
set command=%~1
if "%command%"=="" set command=build

if "%command%"=="build" (
    call :check_dependencies
    call :setup_dirs
    call :build_current
) else if "%command%"=="build-all" (
    call :check_dependencies
    call :setup_dirs
    call :build_all
) else if "%command%"=="test" (
    call :check_dependencies
    call :run_tests
) else if "%command%"=="lint" (
    call :check_dependencies
    call :run_linter
) else if "%command%"=="clean" (
    call :clean
) else if "%command%"=="package" (
    call :check_dependencies
    call :clean
    call :setup_dirs
    call :run_tests
    call :run_linter
    call :build_all
    call :generate_checksums
    call :log_success "Package build completed"
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else (
    call :log_error "Unknown command: %command%"
    call :show_help
    exit /b 1
)

endlocal
