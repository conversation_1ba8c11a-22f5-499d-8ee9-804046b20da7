/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package logger

import (
	"log"
	"os"
	"path/filepath"

	"arien-ai-cli/internal/config"
)

var (
	InfoLogger    *log.Logger
	WarningLogger *log.Logger
	ErrorLogger   *log.Logger
	DebugLogger   *log.Logger
)

// Initialize sets up the logging system
func Initialize() {
	// Get config directory
	configDir, err := config.GetConfigDir()
	if err != nil {
		log.Fatal("Failed to get config directory:", err)
	}

	// Create logs directory
	logsDir := filepath.Join(configDir, "logs")
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		log.Fatal("Failed to create logs directory:", err)
	}

	// Create log file
	logFile := filepath.Join(logsDir, "arien-ai.log")
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Fatal("Failed to open log file:", err)
	}

	// Create loggers
	InfoLogger = log.New(file, "INFO: ", log.Ldate|log.Ltime|log.Lshortfile)
	WarningLogger = log.New(file, "WARNING: ", log.Ldate|log.Ltime|log.Lshortfile)
	ErrorLogger = log.New(file, "ERROR: ", log.Ldate|log.Ltime|log.Lshortfile)
	DebugLogger = log.New(file, "DEBUG: ", log.Ldate|log.Ltime|log.Lshortfile)
}

// Info logs an info message
func Info(v ...interface{}) {
	if InfoLogger != nil {
		InfoLogger.Println(v...)
	}
}

// Warning logs a warning message
func Warning(v ...interface{}) {
	if WarningLogger != nil {
		WarningLogger.Println(v...)
	}
}

// Error logs an error message
func Error(v ...interface{}) {
	if ErrorLogger != nil {
		ErrorLogger.Println(v...)
	}
}

// Debug logs a debug message
func Debug(v ...interface{}) {
	if DebugLogger != nil && config.AppConfig != nil && config.AppConfig.Debug {
		DebugLogger.Println(v...)
	}
}
