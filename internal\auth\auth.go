/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package auth

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/AlecAivazis/survey/v2"
	"github.com/fatih/color"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/logger"
)

// Provider represents an LLM provider
type Provider string

const (
	ProviderDeepSeek   Provider = "deepseek"
	ProviderOpenAI     Provider = "openai"
	ProviderGemini     Provider = "gemini"
	ProviderAnthropic  Provider = "anthropic"
)

// Credentials represents stored authentication credentials
type Credentials struct {
	Providers map[string]ProviderCredentials `json:"providers"`
}

// ProviderCredentials represents credentials for a specific provider
type ProviderCredentials struct {
	APIKey       string `json:"api_key,omitempty"`
	AccessToken  string `json:"access_token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
	ExpiresAt    int64  `json:"expires_at,omitempty"`
	Authenticated bool  `json:"authenticated"`
}

var credentials *Credentials

// Initialize loads stored credentials
func Initialize() error {
	credentialsPath, err := getCredentialsPath()
	if err != nil {
		return err
	}

	credentials = &Credentials{
		Providers: make(map[string]ProviderCredentials),
	}

	// Load existing credentials if file exists
	if _, err := os.Stat(credentialsPath); err == nil {
		data, err := os.ReadFile(credentialsPath)
		if err != nil {
			return fmt.Errorf("failed to read credentials file: %w", err)
		}

		if err := json.Unmarshal(data, credentials); err != nil {
			return fmt.Errorf("failed to parse credentials file: %w", err)
		}
	}

	return nil
}

// IsAuthenticated checks if any provider is authenticated
func IsAuthenticated() bool {
	if credentials == nil {
		Initialize()
	}

	for _, cred := range credentials.Providers {
		if cred.Authenticated {
			return true
		}
	}
	return false
}

// IsProviderAuthenticated checks if a specific provider is authenticated
func IsProviderAuthenticated(provider string) bool {
	if credentials == nil {
		Initialize()
	}

	cred, exists := credentials.Providers[provider]
	return exists && cred.Authenticated
}

// ShowAuthScreen displays the authentication interface
func ShowAuthScreen() error {
	if credentials == nil {
		if err := Initialize(); err != nil {
			return err
		}
	}

	color.Cyan("🚀 Welcome to Arien AI CLI")
	color.White("Please authenticate with your preferred LLM provider(s)")
	fmt.Println()

	// Show current authentication status
	ShowStatus()
	fmt.Println()

	// Provider selection
	var selectedProviders []string
	prompt := &survey.MultiSelect{
		Message: "Select LLM providers to authenticate:",
		Options: []string{
			"DeepSeek (deepseek-chat, deepseek-reasoner)",
			"OpenAI (GPT models)",
			"Google Gemini (OAuth and API key)",
			"Anthropic (Claude models)",
		},
	}

	if err := survey.AskOne(prompt, &selectedProviders); err != nil {
		return err
	}

	// Authenticate selected providers
	for _, selection := range selectedProviders {
		var provider string
		switch selection {
		case "DeepSeek (deepseek-chat, deepseek-reasoner)":
			provider = "deepseek"
		case "OpenAI (GPT models)":
			provider = "openai"
		case "Google Gemini (OAuth and API key)":
			provider = "gemini"
		case "Anthropic (Claude models)":
			provider = "anthropic"
		}

		if err := authenticateProvider(provider); err != nil {
			color.Red("❌ Failed to authenticate %s: %v", provider, err)
			continue
		}
		color.Green("✅ Successfully authenticated %s", provider)
	}

	// Save credentials
	return saveCredentials()
}

// authenticateProvider handles authentication for a specific provider
func authenticateProvider(provider string) error {
	switch provider {
	case "deepseek":
		return authenticateAPIKey(provider, "DeepSeek API Key")
	case "openai":
		return authenticateAPIKey(provider, "OpenAI API Key")
	case "anthropic":
		return authenticateAPIKey(provider, "Anthropic API Key")
	case "gemini":
		return authenticateGemini()
	default:
		return fmt.Errorf("unsupported provider: %s", provider)
	}
}

// authenticateAPIKey handles API key authentication
func authenticateAPIKey(provider, displayName string) error {
	var apiKey string
	prompt := &survey.Password{
		Message: fmt.Sprintf("Enter your %s:", displayName),
	}

	if err := survey.AskOne(prompt, &apiKey); err != nil {
		return err
	}

	if apiKey == "" {
		return fmt.Errorf("API key cannot be empty")
	}

	// Store credentials
	credentials.Providers[provider] = ProviderCredentials{
		APIKey:        apiKey,
		Authenticated: true,
	}

	logger.Info(fmt.Sprintf("Authenticated with %s", provider))
	return nil
}

// authenticateGemini handles Google Gemini authentication with OAuth option
func authenticateGemini() error {
	var authMethod string
	prompt := &survey.Select{
		Message: "Choose authentication method for Google Gemini:",
		Options: []string{
			"API Key",
			"OAuth (recommended for enhanced features)",
		},
	}

	if err := survey.AskOne(prompt, &authMethod); err != nil {
		return err
	}

	switch authMethod {
	case "API Key":
		return authenticateAPIKey("gemini", "Google Gemini API Key")
	case "OAuth (recommended for enhanced features)":
		return authenticateGeminiOAuth()
	default:
		return fmt.Errorf("invalid authentication method")
	}
}

// authenticateGeminiOAuth handles OAuth authentication for Gemini
func authenticateGeminiOAuth() error {
	color.Yellow("🔐 OAuth authentication for Google Gemini")
	color.White("This will open a browser window for authentication...")
	
	// For now, fall back to API key method
	// TODO: Implement full OAuth flow
	color.Yellow("⚠️  OAuth flow not yet implemented. Using API key method instead.")
	return authenticateAPIKey("gemini", "Google Gemini API Key")
}

// Logout removes authentication for specified provider or all providers
func Logout(provider string) error {
	if credentials == nil {
		if err := Initialize(); err != nil {
			return err
		}
	}

	if provider == "" {
		// Logout from all providers
		credentials.Providers = make(map[string]ProviderCredentials)
	} else {
		// Logout from specific provider
		delete(credentials.Providers, provider)
	}

	return saveCredentials()
}

// ShowStatus displays current authentication status
func ShowStatus() {
	if credentials == nil {
		Initialize()
	}

	color.Cyan("🔐 Authentication Status:")
	
	providers := []string{"deepseek", "openai", "gemini", "anthropic"}
	for _, provider := range providers {
		cred, exists := credentials.Providers[provider]
		if exists && cred.Authenticated {
			color.Green("  ✅ %s: Authenticated", provider)
		} else {
			color.Red("  ❌ %s: Not authenticated", provider)
		}
	}
}

// GetCredentials returns credentials for a specific provider
func GetCredentials(provider string) (ProviderCredentials, bool) {
	if credentials == nil {
		Initialize()
	}

	cred, exists := credentials.Providers[provider]
	return cred, exists && cred.Authenticated
}

// getCredentialsPath returns the path to the credentials file
func getCredentialsPath() (string, error) {
	configDir, err := config.GetConfigDir()
	if err != nil {
		return "", err
	}
	return filepath.Join(configDir, "credentials.json"), nil
}

// saveCredentials saves credentials to file
func saveCredentials() error {
	credentialsPath, err := getCredentialsPath()
	if err != nil {
		return err
	}

	data, err := json.MarshalIndent(credentials, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal credentials: %w", err)
	}

	if err := os.WriteFile(credentialsPath, data, 0600); err != nil {
		return fmt.Errorf("failed to write credentials file: %w", err)
	}

	return nil
}
