/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
	"arien-ai-cli/internal/ui"
	"arien-ai-cli/internal/auth"
)

// chatCmd represents the chat command
var chatCmd = &cobra.Command{
	Use:   "chat",
	Short: "Start interactive chat with <PERSON><PERSON>",
	Long:  `Start an interactive chat session with your configured LLM provider.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Check authentication
		if !auth.IsAuthenticated() {
			fmt.Println("Please authenticate first using 'arien-ai auth login'")
			return
		}

		// Get flags
		provider, _ := cmd.Flags().GetString("provider")
		model, _ := cmd.Flags().GetString("model")
		theme, _ := cmd.Flags().GetString("theme")

		// Start chat
		if err := ui.StartChat(provider, model, theme); err != nil {
			fmt.Printf("Error starting chat: %v\n", err)
			return
		}
	},
}

var themeCmd = &cobra.Command{
	Use:   "theme",
	Short: "Configure UI themes",
	Long:  `Configure and preview UI themes for the interactive terminal interface.`,
	Run: func(cmd *cobra.Command, args []string) {
		if err := ui.ShowThemeSelector(); err != nil {
			fmt.Printf("Error showing theme selector: %v\n", err)
			return
		}
	},
}

func init() {
	rootCmd.AddCommand(chatCmd)
	rootCmd.AddCommand(themeCmd)

	// Chat command flags
	chatCmd.Flags().StringP("provider", "p", "", "LLM provider (deepseek, openai, gemini, anthropic)")
	chatCmd.Flags().StringP("model", "m", "", "specific model to use")
	chatCmd.Flags().StringP("theme", "t", "default", "UI theme to use")
}
