/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

import (
	"context"
	"fmt"
	"time"

	"github.com/google/generative-ai-go/genai"
	"google.golang.org/api/option"
	"arien-ai-cli/internal/logger"
)

// GeminiProvider implements the Provider interface for Google Gemini
type GeminiProvider struct {
	client *genai.Client
	config ProviderConfig
}

// NewGeminiProvider creates a new Gemini provider
func NewGeminiProvider(config ProviderConfig) (*GeminiProvider, error) {
	ctx := context.Background()
	
	// Create client with API key
	client, err := genai.NewClient(ctx, option.WithAPIKey(config.APIKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	return &GeminiProvider{
		client: client,
		config: config,
	}, nil
}

// Chat sends a chat completion request to Gemini
func (p *GeminiProvider) Chat(ctx context.Context, request ChatRequest) (*ChatResponse, error) {
	logger.Debug("Gemini Chat request:", request)

	// Get the model
	model := p.client.GenerativeModel(request.Model)
	
	// Configure model parameters
	if request.Temperature > 0 {
		model.Temperature = &[]float32{float32(request.Temperature)}[0]
	}
	if request.MaxTokens > 0 {
		model.MaxOutputTokens = &[]int32{int32(request.MaxTokens)}[0]
	}

	// Convert messages to Gemini format
	var parts []genai.Part
	var systemInstruction string

	for _, msg := range request.Messages {
		switch msg.Role {
		case "system":
			systemInstruction = msg.Content
		case "user":
			parts = append(parts, genai.Text(msg.Content))
		case "assistant":
			// For assistant messages, we need to handle them differently
			// Gemini doesn't have explicit assistant messages in the same way
			parts = append(parts, genai.Text(fmt.Sprintf("Assistant: %s", msg.Content)))
		}
	}

	// Set system instruction if provided
	if systemInstruction != "" {
		model.SystemInstruction = &genai.Content{
			Parts: []genai.Part{genai.Text(systemInstruction)},
		}
	}

	// Add tools if provided
	if len(request.Tools) > 0 && p.SupportsTools() {
		var tools []*genai.Tool
		for _, tool := range request.Tools {
			geminiTool := &genai.Tool{
				FunctionDeclarations: []*genai.FunctionDeclaration{
					{
						Name:        tool.Function.Name,
						Description: tool.Function.Description,
						Parameters:  convertToGeminiSchema(tool.Function.Parameters),
					},
				},
			}
			tools = append(tools, geminiTool)
		}
		model.Tools = tools
	}

	// Generate content
	resp, err := model.GenerateContent(ctx, parts...)
	if err != nil {
		logger.Error("Gemini API error:", err)
		return nil, fmt.Errorf("Gemini API error: %w", err)
	}

	// Convert response to our format
	return p.convertGeminiResponse(resp, request.Model), nil
}

// Stream sends a streaming chat completion request to Gemini
func (p *GeminiProvider) Stream(ctx context.Context, request ChatRequest) (<-chan StreamResponse, error) {
	logger.Debug("Gemini Stream request:", request)

	// Get the model
	model := p.client.GenerativeModel(request.Model)
	
	// Configure model parameters
	if request.Temperature > 0 {
		model.Temperature = &[]float32{float32(request.Temperature)}[0]
	}
	if request.MaxTokens > 0 {
		model.MaxOutputTokens = &[]int32{int32(request.MaxTokens)}[0]
	}

	// Convert messages to Gemini format
	var parts []genai.Part
	var systemInstruction string

	for _, msg := range request.Messages {
		switch msg.Role {
		case "system":
			systemInstruction = msg.Content
		case "user":
			parts = append(parts, genai.Text(msg.Content))
		case "assistant":
			parts = append(parts, genai.Text(fmt.Sprintf("Assistant: %s", msg.Content)))
		}
	}

	// Set system instruction if provided
	if systemInstruction != "" {
		model.SystemInstruction = &genai.Content{
			Parts: []genai.Part{genai.Text(systemInstruction)},
		}
	}

	// Add tools if provided
	if len(request.Tools) > 0 && p.SupportsTools() {
		var tools []*genai.Tool
		for _, tool := range request.Tools {
			geminiTool := &genai.Tool{
				FunctionDeclarations: []*genai.FunctionDeclaration{
					{
						Name:        tool.Function.Name,
						Description: tool.Function.Description,
						Parameters:  convertToGeminiSchema(tool.Function.Parameters),
					},
				},
			}
			tools = append(tools, geminiTool)
		}
		model.Tools = tools
	}

	// Create response channel
	responseChan := make(chan StreamResponse, 10)

	// Start streaming
	go func() {
		defer close(responseChan)

		iter := model.GenerateContentStream(ctx, parts...)
		
		for {
			resp, err := iter.Next()
			if err != nil {
				if err.Error() == "iterator done" {
					responseChan <- StreamResponse{Done: true}
					return
				}
				responseChan <- StreamResponse{Error: err, Done: true}
				return
			}

			// Convert and send response
			streamResp := p.convertGeminiStreamResponse(resp, request.Model)
			responseChan <- streamResp
		}
	}()

	return responseChan, nil
}

// GetModels returns available Gemini models
func (p *GeminiProvider) GetModels() []string {
	return []string{
		"gemini-pro",
		"gemini-pro-vision",
		"gemini-1.5-pro",
		"gemini-1.5-flash",
	}
}

// GetName returns the provider name
func (p *GeminiProvider) GetName() string {
	return "gemini"
}

// SupportsTools returns whether Gemini supports function calling
func (p *GeminiProvider) SupportsTools() bool {
	return true
}

// Helper methods

func (p *GeminiProvider) convertGeminiResponse(resp *genai.GenerateContentResponse, model string) *ChatResponse {
	var content string
	var toolCalls []ToolCall

	if len(resp.Candidates) > 0 {
		candidate := resp.Candidates[0]
		
		for _, part := range candidate.Content.Parts {
			switch v := part.(type) {
			case genai.Text:
				content += string(v)
			case *genai.FunctionCall:
				toolCall := ToolCall{
					ID:   fmt.Sprintf("call_%d", time.Now().UnixNano()),
					Type: "function",
					Function: FunctionCall{
						Name:      v.Name,
						Arguments: fmt.Sprintf("%v", v.Args),
					},
				}
				toolCalls = append(toolCalls, toolCall)
			}
		}
	}

	var usage Usage
	if resp.UsageMetadata != nil {
		usage = Usage{
			PromptTokens:     int(resp.UsageMetadata.PromptTokenCount),
			CompletionTokens: int(resp.UsageMetadata.CandidatesTokenCount),
			TotalTokens:      int(resp.UsageMetadata.TotalTokenCount),
		}
	}

	return &ChatResponse{
		ID:      fmt.Sprintf("gemini-%d", time.Now().UnixNano()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []Choice{{
			Index: 0,
			Message: Message{
				Role:      "assistant",
				Content:   content,
				ToolCalls: toolCalls,
			},
			FinishReason: "stop",
		}},
		Usage: usage,
	}
}

func (p *GeminiProvider) convertGeminiStreamResponse(resp *genai.GenerateContentResponse, model string) StreamResponse {
	var content string
	var toolCalls []ToolCall

	if len(resp.Candidates) > 0 {
		candidate := resp.Candidates[0]
		
		for _, part := range candidate.Content.Parts {
			switch v := part.(type) {
			case genai.Text:
				content += string(v)
			case *genai.FunctionCall:
				toolCall := ToolCall{
					ID:   fmt.Sprintf("call_%d", time.Now().UnixNano()),
					Type: "function",
					Function: FunctionCall{
						Name:      v.Name,
						Arguments: fmt.Sprintf("%v", v.Args),
					},
				}
				toolCalls = append(toolCalls, toolCall)
			}
		}
	}

	return StreamResponse{
		ID:      fmt.Sprintf("gemini-stream-%d", time.Now().UnixNano()),
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []StreamChoice{{
			Index: 0,
			Delta: MessageDelta{
				Role:      "assistant",
				Content:   content,
				ToolCalls: toolCalls,
			},
		}},
	}
}

func convertToGeminiSchema(params map[string]interface{}) *genai.Schema {
	// Convert our parameter schema to Gemini format
	// This is a simplified conversion
	schema := &genai.Schema{
		Type: genai.TypeObject,
	}
	
	if properties, ok := params["properties"].(map[string]interface{}); ok {
		schema.Properties = make(map[string]*genai.Schema)
		for name, prop := range properties {
			if propMap, ok := prop.(map[string]interface{}); ok {
				propSchema := &genai.Schema{}
				if propType, ok := propMap["type"].(string); ok {
					switch propType {
					case "string":
						propSchema.Type = genai.TypeString
					case "number":
						propSchema.Type = genai.TypeNumber
					case "integer":
						propSchema.Type = genai.TypeInteger
					case "boolean":
						propSchema.Type = genai.TypeBoolean
					case "array":
						propSchema.Type = genai.TypeArray
					case "object":
						propSchema.Type = genai.TypeObject
					}
				}
				if desc, ok := propMap["description"].(string); ok {
					propSchema.Description = desc
				}
				schema.Properties[name] = propSchema
			}
		}
	}
	
	if required, ok := params["required"].([]interface{}); ok {
		for _, req := range required {
			if reqStr, ok := req.(string); ok {
				schema.Required = append(schema.Required, reqStr)
			}
		}
	}
	
	return schema
}

// Close closes the Gemini client
func (p *GeminiProvider) Close() error {
	return p.client.Close()
}
