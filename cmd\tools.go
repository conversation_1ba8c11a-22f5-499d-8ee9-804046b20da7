/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package cmd

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cobra"
	"arien-ai-cli/internal/tools"
)

// toolsCmd represents the tools command
var toolsCmd = &cobra.Command{
	Use:   "tools",
	Short: "Tool management and information",
	Long:  `Manage and get information about available tools for LLM function calling.`,
}

var toolsListCmd = &cobra.Command{
	Use:   "list",
	Short: "List available tools",
	Long:  `List all available tools that can be used by LLMs.`,
	Run: func(cmd *cobra.Command, args []string) {
		toolManager := tools.NewManager()
		if err := toolManager.Initialize(); err != nil {
			fmt.Printf("Failed to initialize tools: %v\n", err)
			return
		}

		toolInfos := toolManager.ListTools()
		
		fmt.Printf("Available Tools (%d):\n\n", len(toolInfos))
		for _, tool := range toolInfos {
			fmt.Printf("🔧 %s\n", tool.Name)
			fmt.Printf("   Description: %s\n", tool.Description)
			
			// Show parameters
			if params, ok := tool.Parameters["properties"].(map[string]interface{}); ok {
				fmt.Printf("   Parameters:\n")
				for paramName, paramInfo := range params {
					if paramMap, ok := paramInfo.(map[string]interface{}); ok {
						paramType := "unknown"
						if t, ok := paramMap["type"].(string); ok {
							paramType = t
						}
						paramDesc := ""
						if d, ok := paramMap["description"].(string); ok {
							paramDesc = d
						}
						fmt.Printf("     - %s (%s): %s\n", paramName, paramType, paramDesc)
					}
				}
			}
			fmt.Println()
		}
	},
}

var toolsInfoCmd = &cobra.Command{
	Use:   "info [tool_name]",
	Short: "Get detailed information about a specific tool",
	Long:  `Get detailed information about a specific tool including its parameters and usage.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		toolName := args[0]
		
		toolManager := tools.NewManager()
		if err := toolManager.Initialize(); err != nil {
			fmt.Printf("Failed to initialize tools: %v\n", err)
			return
		}

		tool, exists := toolManager.GetToolByName(toolName)
		if !exists {
			fmt.Printf("Tool '%s' not found\n", toolName)
			return
		}

		fmt.Printf("Tool: %s\n", tool.GetName())
		fmt.Printf("Description: %s\n", tool.GetDescription())
		fmt.Printf("\nParameters Schema:\n")
		
		params, err := json.MarshalIndent(tool.GetParameters(), "", "  ")
		if err != nil {
			fmt.Printf("Failed to format parameters: %v\n", err)
			return
		}
		
		fmt.Println(string(params))
	},
}

var toolsTestCmd = &cobra.Command{
	Use:   "test [tool_name] [args_json]",
	Short: "Test a tool with provided arguments",
	Long:  `Test a tool by executing it with the provided JSON arguments.`,
	Args:  cobra.ExactArgs(2),
	Run: func(cmd *cobra.Command, args []string) {
		toolName := args[0]
		argsJSON := args[1]
		
		toolManager := tools.NewManager()
		if err := toolManager.Initialize(); err != nil {
			fmt.Printf("Failed to initialize tools: %v\n", err)
			return
		}

		fmt.Printf("Testing tool: %s\n", toolName)
		fmt.Printf("Arguments: %s\n\n", argsJSON)
		
		result, err := toolManager.ExecuteTool(cmd.Context(), toolName, argsJSON)
		if err != nil {
			fmt.Printf("Execution error: %v\n", err)
			return
		}

		fmt.Printf("Result:\n")
		fmt.Printf("  Success: %v\n", result.Success)
		if result.Message != "" {
			fmt.Printf("  Message: %s\n", result.Message)
		}
		if result.Error != "" {
			fmt.Printf("  Error: %s\n", result.Error)
		}
		if result.Data != nil {
			fmt.Printf("  Data: %v\n", result.Data)
		}
	},
}

func init() {
	rootCmd.AddCommand(toolsCmd)
	toolsCmd.AddCommand(toolsListCmd)
	toolsCmd.AddCommand(toolsInfoCmd)
	toolsCmd.AddCommand(toolsTestCmd)
}
