/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package ui

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbletea"
	"arien-ai-cli/internal/llm"
	"arien-ai-cli/internal/config"
	"arien-ai-cli/internal/logger"
	"arien-ai-cli/internal/tools"
)

// ChatModel represents the chat interface model
type ChatModel struct {
	llmManager   *llm.Manager
	toolManager  *tools.Manager
	provider     string
	model        string
	messages     []ChatMessage
	input        string
	cursor       int
	width        int
	height       int
	streaming    bool
	currentResp  string
	err          error
	ready        bool
}

// ChatMessage represents a chat message
type ChatMessage struct {
	Role      string
	Content   string
	Timestamp time.Time
	ToolCalls []llm.ToolCall
}

// NewChatModel creates a new chat model
func NewChatModel(provider, model string) *ChatModel {
	return &ChatModel{
		llmManager:  llm.NewManager(),
		toolManager: tools.NewManager(),
		provider:    provider,
		model:       model,
		messages:    []ChatMessage{},
		ready:       false,
	}
}

// Init initializes the chat model
func (m *ChatModel) Init() tea.Cmd {
	return tea.Batch(
		tea.EnterAltScreen,
		func() tea.Msg {
			if err := m.llmManager.Initialize(); err != nil {
				return errMsg{err}
			}
			if err := m.toolManager.Initialize(); err != nil {
				return errMsg{err}
			}
			return readyMsg{}
		},
	)
}

// Update handles messages and updates the model
func (m *ChatModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "esc":
			return m, tea.Quit

		case "enter":
			if m.input != "" && !m.streaming {
				return m, m.sendMessage()
			}

		case "backspace":
			if len(m.input) > 0 && m.cursor > 0 {
				m.input = m.input[:m.cursor-1] + m.input[m.cursor:]
				m.cursor--
			}

		case "left":
			if m.cursor > 0 {
				m.cursor--
			}

		case "right":
			if m.cursor < len(m.input) {
				m.cursor++
			}

		case "home":
			m.cursor = 0

		case "end":
			m.cursor = len(m.input)

		default:
			if len(msg.String()) == 1 && !m.streaming {
				m.input = m.input[:m.cursor] + msg.String() + m.input[m.cursor:]
				m.cursor++
			}
		}

	case readyMsg:
		m.ready = true
		// Add system message
		systemPrompt := config.AppConfig.SystemPrompt
		if systemPrompt != "" {
			m.messages = append(m.messages, ChatMessage{
				Role:      "system",
				Content:   systemPrompt,
				Timestamp: time.Now(),
			})
		}
		return m, nil

	case responseMsg:
		m.streaming = false
		m.currentResp = ""
		m.messages = append(m.messages, ChatMessage{
			Role:      "assistant",
			Content:   msg.content,
			Timestamp: time.Now(),
			ToolCalls: msg.toolCalls,
		})
		return m, nil

	case streamMsg:
		m.currentResp += msg.content
		return m, nil

	case errMsg:
		m.err = msg.err
		m.streaming = false
		return m, nil
	}

	return m, nil
}

// View renders the chat interface
func (m *ChatModel) View() string {
	if !m.ready {
		return StatusStyle().Render("🚀 Initializing Arien AI CLI...")
	}

	var b strings.Builder

	// Header
	header := HeaderStyle().Render(fmt.Sprintf("🤖 Arien AI - %s (%s)", m.provider, m.model))
	b.WriteString(header)
	b.WriteString("\n\n")

	// Messages
	messagesHeight := m.height - 6 // Reserve space for header and input
	messageLines := m.renderMessages()
	
	// Show only the last messages that fit in the available space
	if len(messageLines) > messagesHeight {
		messageLines = messageLines[len(messageLines)-messagesHeight:]
	}
	
	for _, line := range messageLines {
		b.WriteString(line)
		b.WriteString("\n")
	}

	// Current streaming response
	if m.streaming && m.currentResp != "" {
		b.WriteString(AssistantMessageStyle().Render("🤖 " + m.currentResp))
		b.WriteString("\n")
	}

	// Error display
	if m.err != nil {
		b.WriteString(ErrorStyle().Render(fmt.Sprintf("❌ Error: %v", m.err)))
		b.WriteString("\n")
	}

	// Input area
	b.WriteString("\n")
	inputPrompt := PrimaryStyle().Render("💬 You: ")
	inputText := m.input
	if m.cursor < len(inputText) {
		inputText = inputText[:m.cursor] + "│" + inputText[m.cursor:]
	} else {
		inputText += "│"
	}
	
	if m.streaming {
		inputText = MutedStyle().Render("(Waiting for response...)")
	}
	
	b.WriteString(inputPrompt + InputStyle().Render(inputText))

	// Status bar
	b.WriteString("\n")
	status := fmt.Sprintf("Press Ctrl+C to quit | %d messages", len(m.messages))
	b.WriteString(StatusStyle().Render(status))

	return b.String()
}

// renderMessages renders all chat messages
func (m *ChatModel) renderMessages() []string {
	var lines []string
	
	for _, msg := range m.messages {
		if msg.Role == "system" {
			continue // Don't display system messages
		}
		
		timestamp := msg.Timestamp.Format("15:04")
		
		switch msg.Role {
		case "user":
			lines = append(lines, UserMessageStyle().Render(fmt.Sprintf("👤 [%s] %s", timestamp, msg.Content)))
		case "assistant":
			content := msg.Content
			if len(msg.ToolCalls) > 0 {
				content += "\n" + m.renderToolCalls(msg.ToolCalls)
			}
			lines = append(lines, AssistantMessageStyle().Render(fmt.Sprintf("🤖 [%s] %s", timestamp, content)))
		}
		
		lines = append(lines, "") // Add spacing between messages
	}
	
	return lines
}

// renderToolCalls renders function calls
func (m *ChatModel) renderToolCalls(toolCalls []llm.ToolCall) string {
	var parts []string
	for _, call := range toolCalls {
		parts = append(parts, AccentStyle().Render(fmt.Sprintf("🔧 Called: %s(%s)", call.Function.Name, call.Function.Arguments)))
	}
	return strings.Join(parts, "\n")
}

// sendMessage sends a user message to the LLM
func (m *ChatModel) sendMessage() tea.Cmd {
	userInput := m.input
	m.input = ""
	m.cursor = 0
	m.streaming = true
	m.err = nil

	// Add user message
	m.messages = append(m.messages, ChatMessage{
		Role:      "user",
		Content:   userInput,
		Timestamp: time.Now(),
	})

	return func() tea.Msg {
		ctx := context.Background()

		// Convert messages to LLM format
		var llmMessages []llm.Message
		for _, msg := range m.messages {
			llmMessages = append(llmMessages, llm.Message{
				Role:      msg.Role,
				Content:   msg.Content,
				ToolCalls: msg.ToolCalls,
			})
		}

		// Get available tools
		toolDefinitions := m.toolManager.GetToolDefinitions()
		var llmTools []llm.Tool
		for _, toolDef := range toolDefinitions {
			llmTools = append(llmTools, llm.Tool{
				Type:     toolDef.Type,
				Function: llm.Function{
					Name:        toolDef.Function.Name,
					Description: toolDef.Function.Description,
					Parameters:  toolDef.Function.Parameters,
				},
			})
		}

		// Create request with tools
		request := llm.ChatRequest{
			Model:    m.model,
			Messages: llmMessages,
			Tools:    llmTools,
		}

		// Send request
		resp, err := m.llmManager.Chat(ctx, m.provider, request)
		if err != nil {
			logger.Error("Chat error:", err)
			return errMsg{err}
		}

		if len(resp.Choices) > 0 {
			choice := resp.Choices[0]

			// Handle tool calls
			if len(choice.Message.ToolCalls) > 0 {
				return m.handleToolCalls(ctx, choice.Message.ToolCalls, choice.Message.Content)
			}

			return responseMsg{
				content:   choice.Message.Content,
				toolCalls: choice.Message.ToolCalls,
			}
		}

		return errMsg{fmt.Errorf("no response from LLM")}
	}
}

// handleToolCalls executes tool calls and continues the conversation
func (m *ChatModel) handleToolCalls(ctx context.Context, toolCalls []llm.ToolCall, assistantContent string) tea.Msg {
	// Add assistant message with tool calls
	m.messages = append(m.messages, ChatMessage{
		Role:      "assistant",
		Content:   assistantContent,
		Timestamp: time.Now(),
		ToolCalls: toolCalls,
	})

	// Execute each tool call
	for _, toolCall := range toolCalls {
		result, err := m.toolManager.ExecuteTool(ctx, toolCall.Function.Name, toolCall.Function.Arguments)
		if err != nil {
			logger.Error("Tool execution error:", err)
			continue
		}

		// Add tool result as a message
		var resultContent string
		if result.Success {
			resultContent = fmt.Sprintf("Tool executed successfully: %s\nResult: %v", result.Message, result.Data)
		} else {
			resultContent = fmt.Sprintf("Tool execution failed: %s", result.Error)
		}

		m.messages = append(m.messages, ChatMessage{
			Role:      "tool",
			Content:   resultContent,
			Timestamp: time.Now(),
		})
	}

	// Continue conversation with tool results
	var llmMessages []llm.Message
	for _, msg := range m.messages {
		llmMessage := llm.Message{
			Role:      msg.Role,
			Content:   msg.Content,
			ToolCalls: msg.ToolCalls,
		}

		// Handle tool call ID for tool messages
		if msg.Role == "tool" && len(toolCalls) > 0 {
			llmMessage.ToolCallID = toolCalls[0].ID // Simplified - should match specific tool call
		}

		llmMessages = append(llmMessages, llmMessage)
	}

	// Get tools again
	toolDefinitions := m.toolManager.GetToolDefinitions()
	var llmTools []llm.Tool
	for _, toolDef := range toolDefinitions {
		llmTools = append(llmTools, llm.Tool{
			Type:     toolDef.Type,
			Function: llm.Function{
				Name:        toolDef.Function.Name,
				Description: toolDef.Function.Description,
				Parameters:  toolDef.Function.Parameters,
			},
		})
	}

	// Send follow-up request
	request := llm.ChatRequest{
		Model:    m.model,
		Messages: llmMessages,
		Tools:    llmTools,
	}

	resp, err := m.llmManager.Chat(ctx, m.provider, request)
	if err != nil {
		logger.Error("Follow-up chat error:", err)
		return errMsg{err}
	}

	if len(resp.Choices) > 0 {
		choice := resp.Choices[0]
		return responseMsg{
			content:   choice.Message.Content,
			toolCalls: choice.Message.ToolCalls,
		}
	}

	return errMsg{fmt.Errorf("no follow-up response from LLM")}
}

// Message types
type readyMsg struct{}
type responseMsg struct {
	content   string
	toolCalls []llm.ToolCall
}
type streamMsg struct {
	content string
}
type errMsg struct {
	err error
}
