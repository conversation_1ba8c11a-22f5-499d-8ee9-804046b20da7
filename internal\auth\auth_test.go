/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package auth

import (
	"os"
	"testing"
)

func TestInitialize(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Test initialization
	err = Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Check if credentials was created
	if credentials == nil {
		t.Fatal("credentials is nil after initialization")
	}

	if credentials.Providers == nil {
		t.Fatal("credentials.Providers is nil after initialization")
	}
}

func TestIsAuthenticated(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Initialize
	err = Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Should not be authenticated initially
	if IsAuthenticated() {
		t.Error("Should not be authenticated initially")
	}

	// Add a provider
	credentials.Providers["test"] = ProviderCredentials{
		APIKey:        "test-key",
		Authenticated: true,
	}

	// Should be authenticated now
	if !IsAuthenticated() {
		t.Error("Should be authenticated after adding provider")
	}
}

func TestIsProviderAuthenticated(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Initialize
	err = Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Should not be authenticated initially
	if IsProviderAuthenticated("openai") {
		t.Error("Should not be authenticated initially")
	}

	// Add a provider
	credentials.Providers["openai"] = ProviderCredentials{
		APIKey:        "test-key",
		Authenticated: true,
	}

	// Should be authenticated now
	if !IsProviderAuthenticated("openai") {
		t.Error("Should be authenticated after adding provider")
	}

	// Other providers should not be authenticated
	if IsProviderAuthenticated("deepseek") {
		t.Error("Other providers should not be authenticated")
	}
}

func TestGetCredentials(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Initialize
	err = Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Should not get credentials initially
	_, exists := GetCredentials("openai")
	if exists {
		t.Error("Should not get credentials initially")
	}

	// Add a provider
	testCreds := ProviderCredentials{
		APIKey:        "test-key",
		Authenticated: true,
	}
	credentials.Providers["openai"] = testCreds

	// Should get credentials now
	creds, exists := GetCredentials("openai")
	if !exists {
		t.Error("Should get credentials after adding provider")
	}

	if creds.APIKey != "test-key" {
		t.Errorf("Expected API key 'test-key', got '%s'", creds.APIKey)
	}
}

func TestLogout(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "arien-ai-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Set HOME to temp directory
	originalHome := os.Getenv("HOME")
	os.Setenv("HOME", tempDir)
	defer os.Setenv("HOME", originalHome)

	// Initialize
	err = Initialize()
	if err != nil {
		t.Fatalf("Initialize failed: %v", err)
	}

	// Add providers
	credentials.Providers["openai"] = ProviderCredentials{
		APIKey:        "test-key-1",
		Authenticated: true,
	}
	credentials.Providers["deepseek"] = ProviderCredentials{
		APIKey:        "test-key-2",
		Authenticated: true,
	}

	// Logout from specific provider
	err = Logout("openai")
	if err != nil {
		t.Fatalf("Logout failed: %v", err)
	}

	// OpenAI should be logged out
	if IsProviderAuthenticated("openai") {
		t.Error("OpenAI should be logged out")
	}

	// DeepSeek should still be authenticated
	if !IsProviderAuthenticated("deepseek") {
		t.Error("DeepSeek should still be authenticated")
	}

	// Logout from all providers
	err = Logout("")
	if err != nil {
		t.Fatalf("Logout all failed: %v", err)
	}

	// No providers should be authenticated
	if IsAuthenticated() {
		t.Error("No providers should be authenticated after logout all")
	}
}
