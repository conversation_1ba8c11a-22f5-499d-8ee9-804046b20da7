/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package llm

import (
	"context"
	"fmt"
	"io"

	"github.com/sashabaranov/go-openai"
	"arien-ai-cli/internal/logger"
)

// OpenAIProvider implements the Provider interface for OpenAI
type OpenAIProvider struct {
	client *openai.Client
	config ProviderConfig
}

// NewOpenAIProvider creates a new OpenAI provider
func NewOpenAIProvider(config ProviderConfig) *OpenAIProvider {
	clientConfig := openai.DefaultConfig(config.APIKey)
	if config.BaseURL != "" {
		clientConfig.BaseURL = config.BaseURL
	}

	return &OpenAIProvider{
		client: openai.NewClientWithConfig(clientConfig),
		config: config,
	}
}

// Chat sends a chat completion request to OpenAI
func (p *OpenAIProvider) Chat(ctx context.Context, request ChatRequest) (*ChatResponse, error) {
	logger.Debug("OpenAI Chat request:", request)

	// Convert our request to OpenAI format
	req := openai.ChatCompletionRequest{
		Model:       request.Model,
		Messages:    convertMessagesToOpenAI(request.Messages),
		Temperature: float32(request.Temperature),
		MaxTokens:   request.MaxTokens,
	}

	// Add tools if provided
	if len(request.Tools) > 0 {
		req.Tools = convertToolsToOpenAI(request.Tools)
		if request.ToolChoice != "" {
			req.ToolChoice = request.ToolChoice
		}
	}

	// Make the request
	resp, err := p.client.CreateChatCompletion(ctx, req)
	if err != nil {
		logger.Error("OpenAI API error:", err)
		return nil, fmt.Errorf("OpenAI API error: %w", err)
	}

	// Convert response back to our format
	return convertOpenAIResponse(resp), nil
}

// Stream sends a streaming chat completion request to OpenAI
func (p *OpenAIProvider) Stream(ctx context.Context, request ChatRequest) (<-chan StreamResponse, error) {
	logger.Debug("OpenAI Stream request:", request)

	// Convert our request to OpenAI format
	req := openai.ChatCompletionRequest{
		Model:       request.Model,
		Messages:    convertMessagesToOpenAI(request.Messages),
		Temperature: float32(request.Temperature),
		MaxTokens:   request.MaxTokens,
		Stream:      true,
	}

	// Add tools if provided
	if len(request.Tools) > 0 {
		req.Tools = convertToolsToOpenAI(request.Tools)
		if request.ToolChoice != "" {
			req.ToolChoice = request.ToolChoice
		}
	}

	// Create stream
	stream, err := p.client.CreateChatCompletionStream(ctx, req)
	if err != nil {
		logger.Error("OpenAI Stream API error:", err)
		return nil, fmt.Errorf("OpenAI Stream API error: %w", err)
	}

	// Create response channel
	responseChan := make(chan StreamResponse, 10)

	// Start goroutine to handle streaming
	go func() {
		defer close(responseChan)
		defer stream.Close()

		for {
			response, err := stream.Recv()
			if err != nil {
				if err == io.EOF {
					responseChan <- StreamResponse{Done: true}
					return
				}
				responseChan <- StreamResponse{Error: err, Done: true}
				return
			}

			// Convert and send response
			streamResp := convertOpenAIStreamResponse(response)
			responseChan <- streamResp
		}
	}()

	return responseChan, nil
}

// GetModels returns available OpenAI models
func (p *OpenAIProvider) GetModels() []string {
	return []string{
		"gpt-4",
		"gpt-4-turbo",
		"gpt-4-turbo-preview",
		"gpt-3.5-turbo",
		"gpt-3.5-turbo-16k",
	}
}

// GetName returns the provider name
func (p *OpenAIProvider) GetName() string {
	return "openai"
}

// SupportsTools returns whether OpenAI supports function calling
func (p *OpenAIProvider) SupportsTools() bool {
	return true
}

// Helper functions for conversion

func convertMessagesToOpenAI(messages []Message) []openai.ChatCompletionMessage {
	var result []openai.ChatCompletionMessage
	for _, msg := range messages {
		openaiMsg := openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
			Name:    msg.Name,
		}

		// Convert tool calls
		if len(msg.ToolCalls) > 0 {
			for _, tc := range msg.ToolCalls {
				openaiMsg.ToolCalls = append(openaiMsg.ToolCalls, openai.ToolCall{
					ID:   tc.ID,
					Type: openai.ToolTypeFunction,
					Function: openai.FunctionCall{
						Name:      tc.Function.Name,
						Arguments: tc.Function.Arguments,
					},
				})
			}
		}

		if msg.ToolCallID != "" {
			openaiMsg.ToolCallID = msg.ToolCallID
		}

		result = append(result, openaiMsg)
	}
	return result
}

func convertToolsToOpenAI(tools []Tool) []openai.Tool {
	var result []openai.Tool
	for _, tool := range tools {
		result = append(result, openai.Tool{
			Type: openai.ToolTypeFunction,
			Function: &openai.FunctionDefinition{
				Name:        tool.Function.Name,
				Description: tool.Function.Description,
				Parameters:  tool.Function.Parameters,
			},
		})
	}
	return result
}

func convertOpenAIResponse(resp openai.ChatCompletionResponse) *ChatResponse {
	var choices []Choice
	for _, choice := range resp.Choices {
		var toolCalls []ToolCall
		for _, tc := range choice.Message.ToolCalls {
			toolCalls = append(toolCalls, ToolCall{
				ID:   tc.ID,
				Type: string(tc.Type),
				Function: FunctionCall{
					Name:      tc.Function.Name,
					Arguments: tc.Function.Arguments,
				},
			})
		}

		choices = append(choices, Choice{
			Index: choice.Index,
			Message: Message{
				Role:      choice.Message.Role,
				Content:   choice.Message.Content,
				ToolCalls: toolCalls,
			},
			FinishReason: string(choice.FinishReason),
		})
	}

	return &ChatResponse{
		ID:      resp.ID,
		Object:  resp.Object,
		Created: resp.Created,
		Model:   resp.Model,
		Choices: choices,
		Usage: Usage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		},
	}
}

func convertOpenAIStreamResponse(resp openai.ChatCompletionStreamResponse) StreamResponse {
	var choices []StreamChoice
	for _, choice := range resp.Choices {
		var toolCalls []ToolCall
		for _, tc := range choice.Delta.ToolCalls {
			toolCalls = append(toolCalls, ToolCall{
				ID:   tc.ID,
				Type: string(tc.Type),
				Function: FunctionCall{
					Name:      tc.Function.Name,
					Arguments: tc.Function.Arguments,
				},
			})
		}

		choices = append(choices, StreamChoice{
			Index: choice.Index,
			Delta: MessageDelta{
				Role:      choice.Delta.Role,
				Content:   choice.Delta.Content,
				ToolCalls: toolCalls,
			},
			FinishReason: string(choice.FinishReason),
		})
	}

	return StreamResponse{
		ID:      resp.ID,
		Object:  resp.Object,
		Created: resp.Created,
		Model:   resp.Model,
		Choices: choices,
	}
}
