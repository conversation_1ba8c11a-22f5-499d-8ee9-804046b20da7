/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
	"github.com/joho/godotenv"
)

// Config represents the application configuration
type Config struct {
	Debug bool `mapstructure:"debug"`
	
	// Default settings
	DefaultProvider string `mapstructure:"default_provider"`
	DefaultModel    string `mapstructure:"default_model"`
	DefaultTheme    string `mapstructure:"default_theme"`
	
	// System prompt
	SystemPrompt string `mapstructure:"system_prompt"`
	
	// API configurations
	Providers map[string]ProviderConfig `mapstructure:"providers"`
}

// ProviderConfig represents configuration for each LLM provider
type ProviderConfig struct {
	APIKey      string            `mapstructure:"api_key"`
	BaseURL     string            `mapstructure:"base_url"`
	Models      []string          `mapstructure:"models"`
	DefaultModel string           `mapstructure:"default_model"`
	Headers     map[string]string `mapstructure:"headers"`
	OAuth       OAuthConfig       `mapstructure:"oauth"`
}

// OAuthConfig represents OAuth configuration for providers like Google Gemini
type OAuthConfig struct {
	ClientID     string `mapstructure:"client_id"`
	ClientSecret string `mapstructure:"client_secret"`
	RedirectURL  string `mapstructure:"redirect_url"`
	Scopes       []string `mapstructure:"scopes"`
}

var AppConfig *Config

// Initialize sets up the configuration system
func Initialize() error {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		// .env file is optional, so we don't return an error
	}

	// Set default configuration
	setDefaults()

	// Create config directory if it doesn't exist
	configDir, err := getConfigDir()
	if err != nil {
		return fmt.Errorf("failed to get config directory: %w", err)
	}

	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Set up viper
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configDir)
	viper.AutomaticEnv()

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// Config file not found; create default config
			if err := createDefaultConfig(configDir); err != nil {
				return fmt.Errorf("failed to create default config: %w", err)
			}
		} else {
			return fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// Unmarshal config
	AppConfig = &Config{}
	if err := viper.Unmarshal(AppConfig); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return nil
}

// setDefaults sets default configuration values
func setDefaults() {
	viper.SetDefault("debug", false)
	viper.SetDefault("default_provider", "openai")
	viper.SetDefault("default_model", "gpt-4")
	viper.SetDefault("default_theme", "default")
	viper.SetDefault("system_prompt", "You are a helpful AI assistant with access to various tools and functions. You can help users with a wide range of tasks.")
	
	// Provider defaults
	viper.SetDefault("providers.openai.base_url", "https://api.openai.com/v1")
	viper.SetDefault("providers.openai.models", []string{"gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"})
	viper.SetDefault("providers.openai.default_model", "gpt-4")
	
	viper.SetDefault("providers.deepseek.base_url", "https://api.deepseek.com/v1")
	viper.SetDefault("providers.deepseek.models", []string{"deepseek-chat", "deepseek-reasoner"})
	viper.SetDefault("providers.deepseek.default_model", "deepseek-chat")
	
	viper.SetDefault("providers.anthropic.base_url", "https://api.anthropic.com/v1")
	viper.SetDefault("providers.anthropic.models", []string{"claude-3-opus", "claude-3-sonnet", "claude-3-haiku"})
	viper.SetDefault("providers.anthropic.default_model", "claude-3-sonnet")
	
	viper.SetDefault("providers.gemini.base_url", "https://generativelanguage.googleapis.com/v1")
	viper.SetDefault("providers.gemini.models", []string{"gemini-pro", "gemini-pro-vision"})
	viper.SetDefault("providers.gemini.default_model", "gemini-pro")
}

// getConfigDir returns the configuration directory path
func getConfigDir() (string, error) {
	home, err := os.UserHomeDir()
	if err != nil {
		return "", err
	}
	return filepath.Join(home, ".arien-ai"), nil
}

// createDefaultConfig creates a default configuration file
func createDefaultConfig(configDir string) error {
	configPath := filepath.Join(configDir, "config.yaml")
	
	// Write default config to file
	if err := viper.WriteConfigAs(configPath); err != nil {
		return err
	}
	
	return nil
}

// Save saves the current configuration to file
func Save() error {
	return viper.WriteConfig()
}

// GetConfigDir returns the configuration directory path
func GetConfigDir() (string, error) {
	return getConfigDir()
}
